/* eslint-disable camelcase */
/* eslint-disable object-shorthand */
/* eslint-disable react/no-danger */
/* eslint-disable jsx-a11y/role-has-required-aria-props */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-key */
/* eslint-disable consistent-return */
/* eslint-disable func-names */
/* eslint-disable arrow-body-style */
/* eslint-disable no-alert */
/* eslint-disable react-hooks/exhaustive-deps */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import MenuItem from '@mui/material/MenuItem';
import {
    TextField, Button, Box, Badge, Avatar, Typography, IconButton, FormHelperText, InputLabel, Select, FormControl, Grid, Dialog,
    DialogActions, DialogContent, Tooltip,
    Checkbox,
    CardContent, CircularProgress,
    DialogTitle, Alert, Radio, RadioGroup, FormControlLabel, FormGroup, CardActionArea, Card
} from "@mui/material";
import { v4 as uuidv4 } from 'uuid';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import EditIcon from '@mui/icons-material/Edit';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { LoadingButton } from '@mui/lab';
import CloseIcon from '@mui/icons-material/Close';
import ClearIcon from '@mui/icons-material/Clear';
import { useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom'
import { DropzoneArea } from 'material-ui-dropzone';
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import KeyboardDoubleArrowRightIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import DOMPurify from 'dompurify';
import { makeStyles } from '@mui/styles';
import katex from "katex";
import SnackBar from '../../../components/snackbar/snackbar';
import adminServices from '../../../services/adminServices';
import Page from '../../../components/Page'
import PageHeader from '../../../components/PageHeader';
import DialogModal from '../../../components/modal/DialogModal';
import './styles.css'
import "katex/dist/katex.min.css";

window.katex = katex;

const modules = {
    toolbar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ script: "sub" }, { script: "super" }],
        [{ header: [1, 2, 3, false] }],
        ["image", { formula: { customClass: 'qlformula' } }],
        [{ color: [] }, { background: [] }],
        [{ align: [] }],
        ["clean"],
    ],
    clipboard: {
        matchVisual: false,
    },
};

const formats = [
    "header",
    "font",
    "size",
    "bold",
    "italic",
    "underline",
    "strike",
    "blockquote",
    "list",
    "bullet",
    "indent",
    "link",
    "image",
    "video",
    "formula",
    "color",
    "background",
    "align",
    "code-block",
    "script",
    "clean",
];


const SatTestUpdate = () => {
    const userInfo = useSelector((state) => state.userInfo && state.userInfo);
    const navigate = useNavigate();
    const location = useLocation();
    const classes = useStyles();
    const [loading, setLoading] = useState(false);
    const [questionList, setQuestionList] = useState([]);
    const [category, setCategory] = useState('');
    const [openSnackbar, setOpenSnackbar] = useState(false);
    const [snackbarTitle, setSnackbarTitle] = useState('');
    const [name, setName] = useState('');
    const [questionid, setQuestionid] = useState('');
    const [questionDetails, setQuestionDetails] = useState({
        justification: '',
        level: '',
        options: [],
        question_text: '',
        question_type: '',
        cognitive_skill_id: ''
    });
    const [editDetails, setEditDetails] = useState('');
    const [complexity, setComplexity] = useState('');
    const [nameError, setNameError] = useState('');
    const [ImageError, setImageError] = useState('');
    const [descriptionError, setDescriptionError] = useState('');
    const [selectedQuestions, setSelectedQuestions] = useState([]);
    const [ComplexityError, setComplexityError] = useState('');
    // eslint-disable-next-line no-unused-vars
    const [error, setError] = useState(false);
    const [requiredErrors, setRequiredErrors] = useState({
        moduleName: "",
        points: "",
        questionid: "",
    });
    const [detailsError, setDetailsError] = useState({
        level: '',
        questionType: ''
    });
    const [thumbImage, setThumbImage] = useState(null);
    const [thumbPreview, setThumbPreview] = useState(null);
    const [editorValue, setEditorValue] = useState('');
    const [openDialog, setOpenDialog] = useState(false);
    const [OpenDialogNew, setOpenDialogNew] = useState(false);
    const [editData, setEditData] = useState("");
    const [editIndex, setEditIndex] = useState("");
    const [editIndexnew, setEditIndexnew] = useState("");
    const [shortdescription, setShortDescription] = useState('');
    const [editDialog, setEditDialog] = useState(false);
    const [moduleName, setModuleName] = useState('');
    const [selectIndex, setSelectIndex] = useState('');
    const [passage, setPassage] = useState('');
    const [points, setPoints] = useState('');
    const [submitQuestionClicked, setSubmitQuestionClicked] = useState(false);
    const [satvalues, setSatValues] = useState({
        question: '',
        questionType: 'English',
        mcqOptions: [{ option: '', isCorrect: false }],
    });
    const [mathsvalues, setMathsValues] = useState({
        question: '',
        questionType: 'Maths',
        mcqOptions: [{ option: '', isCorrect: false }],
    });
    const [visible, setVisible] = useState(true);
    const [deleteOpen, setDeleteOpen] = useState(false);
    const [deleteData, setDeleteData] = useState('');


    const [open, setOpen] = useState(false);
    const [moduleData, setModuleData] = useState([]);


    const [errorMessage, setErrorMessage] = useState("");
    const [errorMessageAll, setErrorMessageAll] = useState("");
    const [selectedOption, setSelectedOption] = useState('create');
    const [selectedSkills, setSelectedSkills] = useState('');
    const [loadingnew, setLoadingnew] = useState(false);
    const [submitted, setSubmitted] = useState(false);
    // eslint-disable-next-line no-unused-vars
    const [query, setQuery] = useState('');
    const [isFree, setIsFree] = useState(false);
    // eslint-disable-next-line no-unused-vars
    const [selectedOptionnew, setSelectedOptionnew] = useState(null);
    const [hasCertificate, setHasCertificate] = useState(false);
    const [Preview, setPreview] = useState(false);

    const [previewData, setPreviewData] = useState('');
    console.log(previewData, "previewData");


    const [details, setDetails] = useState({ level: "", questionType: "English" });
    const [satData, setSatData] = useState('');
    const [explanation, satExplanation] = useState('');
    const [explanationError, setExplanationError] = useState('');
    const [submitError, setSubmitError] = useState('');
    const [mathsmodel, setMathsModel] = useState(false);

    // console.log(editDetails,"editDetails?.optionseditDetails?.options");

    useMemo(() => {
        if (editDetails) {
            const optionsData = JSON.parse(editDetails?.options);
            const formattedOptions = optionsData.mcqOptions.map((optionText, index) => ({
                option: optionText,
                isCorrect: optionsData.correctAnswer[index] || false,
            }));

            const cognitiveSkillMap = {
                1: "Knowledge",
                2: "Comprehension",
                3: "Application",
                4: "Analysis",
                5: "Synthesis",
                6: "Evaluation"
            };
            setQuestionDetails({
                justification: editDetails.justification,
                level: editDetails.level,
                options: formattedOptions,
                question_text: editDetails.question_text,
                question_type: editDetails.question_type,
                cognitive_skill_id: cognitiveSkillMap[editDetails.cognitive_skill_id]
            });
        }

    }, [editDetails])


    const [time, setTime] = useState({
        hours: 0,
        minutes: 0,
        seconds: 0,
    });
    const [loadingQuestion, setLoadingQuestion] = useState(false);
    const [loadingQuestionnew, setLoadingQuestionnew] = useState(false);

    const [search, setSearch] = useState('');
    const [page, setPage] = useState(0);
    const [questionError, setQuestionError] = useState({
        passage: "",
        question: "",
        option: "",
        Objective: '',
        explanation: ""
    });







    useEffect(async () => {
        const details = await adminServices.getSatDetailsById(location.state)
        setSatData(details.data)




    }, [location.state])

    const ClearError = () => {
        setRequiredErrors({
            moduleName: "",
            points: "",
            questionid: "",
        });
    }




    const handleChangeCheck = (event) => {
        setQuestionError(
            {
                Objective: ''
            }
        )
        setSelectedSkills(event.target.value);
    };
    const handleChangeDescription = (value) => {
        const cleanedValue = value
            .replace(/<p><br><\/p>/g, '')
            .replace(/<p><\/p>/g, '')
            .trim();
        setDescriptionError("");
        setEditorValue(cleanedValue);
    };

    const handlePreview = () => {
        setPreview(false)
        setEditIndexnew('')
        setPreviewData('')
    }
    const handlePreviewOpen = async (data, index) => {
        setEditIndexnew(index)
        setPreview(true)
        if (data && data?.questions_list && data?.questions_list?.length > 0) {
            const val = await adminServices.getMultipleQuestionDetails(data.questions_list);
            data.questiondetails = data?.questions_list?.map(id => {
                return val.data.find(question => question.id === id);
            });
        }
        setPreviewData(data)
    }
    const DeleteQuestion = (index) => {
        setPreviewData((prev) => ({
            ...prev,
            questions_list: prev?.questions_list.filter((_, i) => i !== index),
            questiondetails: prev?.questiondetails.filter((_, i) => i !== index)
        }));
    };

    const handleEditIndividualQuestion = async (data, id) => {
        const result = await adminServices.getQuestionDetails(id)
        //  const mathsedit = result.data?.id[0]
        const mathsedit = result.data.rows[0];
        const val = mathsedit[0]
        setEditDetails(val);
        setLoadingQuestionnew(false)
        setMathsModel(true)
        setPreview(false)
    }

    const handleDelete = (data, inx) => {
        setDeleteData(data)
        setDeleteOpen(true)
    }
    const conformDelete = () => {
        deleteModule(deleteData)
    }
    const handleDeleteClose = () => {
        setDeleteData('')
        setDeleteOpen(false)
    }
    const deleteModule = async (data) => {
        if (data?.moduleId) {
            try {
                const response = await adminServices.deleteSATAssessmentModule(data.moduleId, location.state, data.questions_list);
                if (response.ok) {
                    setDeleteOpen(false)
                    setDeleteData('')
                    setSnackbarTitle('SAT Assessment Module Deleted successfully');
                    setOpenSnackbar(true);
                    setTimeout(() => {
                        navigate("/app/satpage")
                    }, 2000);
                    setLoading(false);
                }
                setDeleteData('')

                setDeleteOpen(false)

            } catch (error) {
                setLoading(false);
            }
        }

        else {
            const result = moduleData.filter(item => item.moduleId);
            setModuleData(result)
            setDeleteOpen(false)
        }

    }
    const UpdateModules = async (data, index) => {
        delete data.questiondetails
        try {
            const response = await adminServices.updateSatAssessmentModule(data, location.state, userInfo.id);
            if (response.ok) {
                setSnackbarTitle('SAT Assessment Module updated successfully');
                setOpenSnackbar(true);
                setTimeout(() => {
                    navigate("/app/satpage")
                }, 2000);
                setLoading(false);
            }

        } catch (error) {
            setLoading(false);
        }
    }

    const handleUpdateQuestions = (editIndexnew) => {
        setModuleData((prevModuleData) =>
            prevModuleData.map((item, index) =>
                index === editIndexnew ? { ...item, ...previewData } : item
            )
        );


        setPreview(false);
    };

    const handleLeftArrowClick = () => {
        setLoadingQuestion(true)
        setPage(prev => prev - 1);
        getQuestionList(details.questionType, search)
    }
    const handleRightArrowClick = () => {
        setLoadingQuestion(true)
        setPage(prev => prev + 1);
        getQuestionList(details.questionType, search)
    }
    const handlePoints = (e) => {
        const value = e.target.value.replace(/\D/, '');
        if (value === '0') {
            setRequiredErrors({
                points: "Value cannot be 0",
            })
        } else {
            setRequiredErrors({
                points: "",
            })
        }
        setPoints(value);
    }



    useEffect(() => {
        setLoadingQuestion(true)
        setLoading(false);
        getQuestionList(details.questionType, search);
    }, [details.questionType, search]);



    useMemo(async () => {
        if (satData && satData?.assessmentData) {
            setName(satData?.assessmentData?.title)
            setComplexity(satData?.assessmentData?.complexity_level)
            const totalSeconds = satData?.assessmentData?.time_in_mins
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const remainingSeconds = totalSeconds % 60;
            setTime({
                hours: hours || 0,
                minutes: minutes || 0,
                seconds: remainingSeconds || 0,
            })
            setThumbPreview(satData?.assessmentData?.image_name)
            setThumbImage(satData?.assessmentData?.image_name)
            setEditorValue(satData?.assessmentData?.short_description)
            setShortDescription(satData?.assessmentData?.highlighted_section)
        }


        // if (satData && satData?.detailedAssessmentData) {
        // const questionList = satData?.detailedAssessmentData?.map(
        //     ({ module_name, weightage, am_questions_list, id, module_type }) => ({
        //       name: module_name,
        //       weightage: parseInt(weightage, 10),
        //       questions_list: am_questions_list,
        //       moduleId: id,
        //       type: module_type,
        //     })
        //   );

        //   const enrichedModules = await Promise.all(
        //     questionList.map(async (module) => {
        //       if (module.questions_list?.length) {
        //         const details = await adminServices.getMultipleQuestionDetails(module.questions_list);
        //         console.log(details,"detailsdetails");

        //         if(details.ok){

        //         return {
        //           ...module,
        //           questiondetails: details?.data?.map((q) => q?.details?.question_text) || [],
        //         };
        //     }
        //       }
        //       return {
        //         ...module,
        //         questiondetails: [],
        //       };
        //     })
        //   );

        //   setModuleData(enrichedModules);
        // }


        if (satData && satData?.detailedAssessmentData) {
            // eslint-disable-next-line camelcase
            const questionList = satData?.detailedAssessmentData?.map(({ module_name, weightage, am_questions_list, id, module_type }) => ({
                // eslint-disable-next-line camelcase
                name: module_name,
                // eslint-disable-next-line no-undef
                weightage: parseInt(weightage, 10),
                // eslint-disable-next-line camelcase
                questions_list: am_questions_list,
                // eslint-disable-next-line camelcase
                // questiondetails: detailed_am_questions_list?.length > 0 ? detailed_am_questions_list?.map((data) => data?.details?.question_text) : [],
                moduleId: id,
                // eslint-disable-next-line camelcase
                type: module_type
            }));
            setModuleData(questionList)



        }


    }, [satData])



    const modelClose = () => {
        setMathsModel(false)
        setLoadingQuestionnew(false)
    }

    const sanitizeConfig = {
        ALLOWED_TAGS: ['b', 'i', 'em', 'a', 'ul', 'ol', 'li'],
    };


    const getQuestionList = async (data, search) => {
        const result = await adminServices.getSatQuestion(data, search, page)
        setQuestionList(result.data)
        setLoadingQuestion(false)

    };

    const handleChangeMathsQuestion = (field, value) => {
        setQuestionError({
            Objective: '',
            question: ''
        });
        setMathsValues((prevState) => ({
            ...prevState,
            [field]: value,
        }));
    }


    const mcqOptionsRef = useRef(satvalues.mcqOptions);

    useEffect(() => {
        mcqOptionsRef.current = satvalues.mcqOptions;
    }, [satvalues.mcqOptions]);

    const mcqOptionsmathsRef = useRef(mathsvalues.mcqOptions);

    useEffect(() => {
        mcqOptionsmathsRef.current = mathsvalues.mcqOptions;
    }, [mathsvalues.mcqOptions]);


    // const handleMCQOptionChangeMaths = (index, field, value) => {

    //     const newMCQOptions = [...mcqOptionsmathsRef.current];
    //     newMCQOptions[index][field] = value;
    //     setMathsValues((prevState) => ({
    //         ...prevState,
    //         mcqOptions: newMCQOptions,
    //     }));
    // };

    const handleMCQOptionChangeMaths = (index, field, value) => {
        const newMCQOptions = [...mcqOptionsmathsRef.current];
        if (!newMCQOptions[index]) {
            newMCQOptions[index] = {};
        }
        newMCQOptions[index][field] = value;
        setMathsValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };

    const handleQuillChange = (name, value) => {
        setQuestionDetails((prev) => ({
            ...prev,
            [name]: value
        }));
    };

    const handleMCQOptionChangeEnglish = (index, field, value) => {

        const newMCQOptions = [...mcqOptionsRef.current];
        newMCQOptions[index][field] = value;
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));

    };

    const handleRemoveOptionMaths = (index) => {
        const newMCQOptions = [...mathsvalues.mcqOptions];
        newMCQOptions.splice(index, 1);
        setMathsValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };


    const handleRemoveOptionEnglish = (index) => {
        const newMCQOptions = [...satvalues.mcqOptions];
        newMCQOptions.splice(index, 1);
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: newMCQOptions,
        }));
    };

    const handleAddOptionMaths = () => {
        const newOption = {
            id: uuidv4(),
            option: '',
            isCorrect: false,
        };
        setMathsValues((prevState) => ({
            ...prevState,
            mcqOptions: [...prevState.mcqOptions, newOption],
        }));
    };


    const handleFileChange = (file) => {
        setImageError("")
        if (file[0]?.size < 2097152) {
            imageWidthAndHeight(file[0]).then((res) => {
                if (res.width >= 360 && res.height >= 200) {
                    const url = URL.createObjectURL(file[0]);
                    setThumbPreview(url);
                    setThumbImage(file[0]);
                } else {
                    alert("Image dimensions must be at least 360x200px.");
                }
            });
        }

    };

    const imageWidthAndHeight = (file) => {
        return new Promise((resolve) => {
            const img = new Image();
            const reader = new FileReader();

            reader.onload = function () {
                img.onload = function () {
                    resolve({ width: img.width, height: img.height });
                };
                img.src = reader.result;
            };
            reader.readAsDataURL(file);
        });
    };



    const handleChangeQuestionEnglish = (field, value) => {
        setQuestionError({
            Objective: ''
        });
        setSatValues((prevState) => ({
            ...prevState,
            [field]: value,
        }));
    };

    const handleAddOptionEnglish = () => {
        const newOption = {
            id: uuidv4(),
            option: '',
            isCorrect: false,
        };
        setSatValues((prevState) => ({
            ...prevState,
            mcqOptions: [...prevState.mcqOptions, newOption],
        }));
    };

    const stripHtml = (html) => {
        const div = document.createElement("div");
        div.innerHTML = html;
        return div.textContent || div.innerText || "";
    };

    const isEmptyHtmlContent = (html) => {
        return stripHtml(html).trim() === "";
    };

    const handleQuestionSubmit = () => {
        if (details?.questionType === 'English') {
            const nonEmptyOptions = satvalues.mcqOptions.filter(opt => opt.option && opt.option.trim() !== '');

            if (
                !passage &&
                !selectedSkills && !explanation &&
                (!satvalues?.question?.trim() || !satvalues?.mcqOptions?.length ||
                    satvalues.mcqOptions.some(option => !option.option.trim()) ||
                    !satvalues.mcqOptions.some(option => option.isCorrect === true))
            ) {
                setQuestionError({
                    passage: "This field is required",
                    question: "This field is required",
                    Objective: "Please Select Level of Objective",
                    explanation: "This field is required"
                });
                return false;
            }
            if (!details.level || isEmptyHtmlContent(details.level)) {
                setDetailsError({ level: 'Please select a level' });
                return false;
            }
            setDetailsError({});

            if (!passage || isEmptyHtmlContent(passage)) {
                setQuestionError({ passage: "This field is required" });
                return false;
            }

            if (!satvalues?.question || isEmptyHtmlContent(satvalues.question)) {
                setQuestionError({ question: "This field is required" });
                return false;
            }

            if (!selectedSkills) {
                setQuestionError({ Objective: "Please Select Level of Objective" });
                return false;
            }

            if (
                !satvalues?.mcqOptions?.length ||
                satvalues.mcqOptions.some(option => isEmptyHtmlContent(option.option))
            ) {
                setQuestionError({ option: "Each option must be filled" });
                return false;
            }
            if (satvalues.mcqOptions.some(option => option.option.trim()) && !satvalues.mcqOptions.some(option => option.isCorrect === true)) {
                setQuestionError({ correctAnswer: "At least one option must be selected as correct" });
                return false;
            }
            if (nonEmptyOptions.length < 4) {
                setQuestionError({ option: "Please add at least 4 options" });
                return false;
            }
            if (!explanation) {
                setQuestionError({ explanation: "This field is required" });
                return false;
            }

            return true;
        }

        if (details?.questionType === 'Maths') {
            const nonEmptyOptions = mathsvalues.mcqOptions.filter(opt => opt.option && opt.option.trim() !== '');

            if (
                !selectedSkills && !explanation &&
                (!mathsvalues?.question?.trim())
            ) {
                setQuestionError({
                    question: "This field is required",

                    Objective: "Please Select Level of Objective",
                });
                return false;
            }


            if (!details.level || isEmptyHtmlContent(details.level)) {
                setDetailsError({ level: 'Please select a level' });
                return false;
            }
            setDetailsError({});


            if (!mathsvalues?.question?.trim()) {
                setQuestionError({ question: "This field is required" });
                return false;
            }

            if (!selectedSkills) {
                setQuestionError({ Objective: "Please Select Level of Objective" });
                return false;
            }

            if (!mathsvalues?.mcqOptions?.length || mathsvalues.mcqOptions.some(option => !option.option.trim())) {
                setQuestionError({ option: "Each option must be filled" });
                return false;
            }

            if (mathsvalues.mcqOptions.some(option => option.option.trim()) && !mathsvalues.mcqOptions.some(option => option.isCorrect === true)) {
                setQuestionError({ correctAnswer: "At least one option must be selected as correct" });
                return false;
            }
            if (nonEmptyOptions.length < 4) {
                setQuestionError({ option: "Please add at least 4  options" });
                return false;
            }
            if (!explanation) {
                setQuestionError({ explanation: "This field is required" });
                return false;
            }
            return true;
        }
    };





    const Validation = () => {
        if (!name) {
            setNameError("This field is required")
            return false
        }
        if (!complexity) {
            setComplexityError("This field is required")
            return false
        }

        if (!thumbImage) {
            setImageError("This field is required")
            return false
        }
        if (!editorValue) {
            setDescriptionError("This field is required")
            return false
        }
        if (editorValue?.length > 255) {
            setDescriptionError("Description cannot be more than 255 characters");
            return false;
        }
        if (moduleData?.length === 0) {
            setErrorMessage("Please fill out the question and weightage before adding a new one.");
            return false
        }
        const totalWeightage = moduleData.reduce((acc, module) => acc + parseFloat(module.weightage || 0), 0);

        if (totalWeightage !== 200) {
            setErrorMessage("The total weightage must be exactly 200.");
            return false;
        }
        return true
    }



    const handleUpdate = async () => {
        const valid = Validation()
        if (valid) {
            setLoading(true);
            // eslint-disable-next-line no-unused-vars
            const totalSeconds = (time.hours * 3600) + (time.minutes * 60) + time.seconds;
            const selectedQuestions = moduleData && moduleData?.map(module => module.questions_list).flat()
            moduleData.forEach((data) => {
                delete data.questiondetails;
            });
            const formData = new FormData();
            formData.append('name', name);
            formData.append('complexity_level', complexity);
            formData.append('description', editorValue);
            // formData.append('time_in_mins', totalSeconds);
            formData.append('time_in_mins', 7920);
            formData.append('thumbImage', thumbImage);
            formData.append('modules', JSON.stringify(moduleData));
            formData.append('is_published', false);
            formData.append('userId', userInfo.id);
            formData.append('selectedQuestions', selectedQuestions);
            formData.append('is_free', isFree);
            formData.append('hasCertificate', hasCertificate);
            formData.append("short_description", shortdescription);

            try {
                const response = await adminServices.updateSatAssessment(formData, location.state);
                if (response.ok) {
                    // setSubmitted(true);
                    setSnackbarTitle('SAT Assessment updated successfully');
                    setOpenSnackbar(true);
                    setTimeout(() => {
                        navigate("/app/satpage")
                    }, 2000);
                    CloseFunction()
                    setModuleData([])


                    setName('')
                    setLoading(false);
                }

            } catch (error) {
                console.log(error);
            }

        }
    };

    const CloseFunction = () => {
        setComplexity("")
        setCategory("")
        setName("")
        setEditorValue("")
        setQuestionList([])
        setNameError("")
        setComplexityError("")
        setImageError("")
        setDescriptionError("")
        setThumbPreview(null)
        setThumbImage(null)
        setQuestionError({
            passage: "",
            question: "",
            option: "",
            Objective: '',
            explanation: ''
        });
        setRequiredErrors({
            questionid: "",
        })
    }


    const handleChange = (event) => {
        const { name, value } = event.target;
        setTime((prevTime) => ({
            ...prevTime,
            [name]: value,
        }));
    };

    const hoursArray = Array.from({ length: 24 }, (_, i) => i);
    const minutesArray = Array.from({ length: 60 }, (_, i) => i);
    const secondsArray = Array.from({ length: 60 }, (_, i) => i);

    const handleDialogOpen = (data) => {

        setErrorMessageAll("")
        if (!name || !complexity || !editorValue) {
            setErrorMessageAll("Please fill out the Above Details.");
        } else {
            setDetails(prevDetails => ({
                ...prevDetails,
                questionType: data
            }));

            setOpen(true);
        }

    };

    const handleClose = () => {
        setPage(0)
        setModuleName('');
        setPoints("")
        setSubmitted(false);
        setQuestionid('')
        setSelectedQuestions([])
        setVisible(true);
        setSelectedOption('create')
        setDetails({ level: "", questionType: "English" });
        setSelectedSkills("")
        setPassage("")

        satExplanation('');
        setSatValues({});
        setMathsValues({});
        setTimeout(() => {

            setSatValues({
                question: '',
                questionType: 'English',
                mcqOptions: [{ option: '', isCorrect: false }],
            });
            setMathsValues({
                question: '',
                questionType: 'Maths',
                mcqOptions: [{ option: '', isCorrect: false }],
            });
        }, 0);
        setSelectedSkills("");
        setOpen(false);
        setRequiredErrors({})
        setOpen(false);
        setQuestionError({
            passage: "",
            question: "",
            option: "",
            Objective: '',
            explanation: ''


        });
        setRequiredErrors({
            questionid: "",
        })
    };
    const handleCloseNew = () => {
        setPage(0)
        setModuleName('');
        setPoints("")
        setSubmitted(false);
        setQuestionid('')
        setSelectedOption('create')
        setSelectedQuestions([])
        setSelectedSkills("")
        setPassage("")
        satExplanation('');
        setSatValues({});
        setMathsValues({});
        setTimeout(() => {

            setSatValues({
                question: '',
                questionType: 'English',
                mcqOptions: [{ option: '', isCorrect: false }],
            });
            setMathsValues({
                question: '',
                questionType: 'Maths',
                mcqOptions: [{ option: '', isCorrect: false }],
            });
        }, 0);
        setSelectedSkills("");
        setOpenDialog(false);
        setOpenDialogNew(false);
        setRequiredErrors({})
        setSubmitted(false);
        setOpenDialog(false);
        setOpenDialogNew(false);
        setVisible(true);
        setQuestionError({
            passage: "",
            question: "",
            option: "",
            Objective: '',
            explanation: ''


        });
        setRequiredErrors({
            questionid: "",
        })
    };
    const handleCloseMaths = () => {
        setModuleName('');
        setSubmitted(false);
        setPoints("")
        setQuestionid('')
        setVisible(true);
        setSelectedOption('create')
        setSelectedQuestions([])
        setDetails({ level: "", questionType: "English" });
        setSelectedSkills("")
        setPassage("")
        satExplanation('');
        setSatValues({});
        setTimeout(() => {

            setSatValues({
                question: '',
                questionType: 'English',
                mcqOptions: [{ option: '', isCorrect: false }],
            });
            setMathsValues({
                question: '',
                questionType: 'Maths',
                mcqOptions: [{ option: '', isCorrect: false }],
            });
        }, 0);
        setSelectedSkills("");
        setOpenDialog(false);
        setOpenDialogNew(false);
        setQuestionError({
            passage: "",
            question: "",
            option: "",
            Objective: '',
            explanation: ''


        });
        setRequiredErrors({
            questionid: "",
        })
    };


    const handleCloseEdit = () => {
        setPage(0)
        setRequiredErrors({})
        setSubmitted(false);
        setEditDialog(false);
        setEditData("")
        setEditIndex("")
    };

    const EditModule = (e) => {
        const { name, value } = e.target;
        setEditData((prev) => ({
            ...prev,
            [name]: value
        }));
    };

    const handleModuleUpdateQuestion = () => {
        const updatedModules = [...moduleData];
        updatedModules[editIndex] = editData;
        setModuleData(updatedModules);
        setEditDialog(false);
    };









    const handleAddNewQuestion = (index, data) => {
        // const filteredQuestions = questionList.filter((item) => {
        //     return data.questions_list.includes(item.id);
        // });
        setQuestionid(data.questions_list)
        // setSelectedQuestions(filteredQuestions)
        if (data?.type === 'English') {
            setSubmitted(false);
            setOpenDialog(true);
            setSelectIndex(index)
            setDetails(prevDetails => ({
                ...prevDetails,
                questionType: 'English'
            }));
        } else {
            setSubmitted(false);
            setOpenDialogNew(true);
            setSelectIndex(index)
            setDetails(prevDetails => ({
                ...prevDetails,
                questionType: 'Maths'
            }));
        }

    }


    const handleEditDetails = (data, index) => {
        setEditData(data)
        setEditIndex(index)
        setEditDialog(true)
    }

    const QuestionValidation = () => {
        const requiredErrors = { moduleName: "", points: "", questionid: "" };
        const tempDetailsError = { level: '' };
        let isValid = true;

        if (!moduleName && !points && questionid?.length === 0 && !selectedSkills && !details.level) {
            setRequiredErrors({ moduleName: "Name is required!", points: "Weightage is required!", questionid: "Please Submit question and Answer!" })
            setQuestionError({ Objective: "Please Select Level of Objective" });
            setDetailsError({ level: 'Please select a level' });
            return false;
        }
        if (!moduleName) {
            requiredErrors.moduleName = "Name is required!";
        }
        if (!selectedSkills) {
            setQuestionError({ Objective: "Please Select Level of Objective" });
            return false;
        }
        if (!points) {
            requiredErrors.points = "Weightage is required!";
        }
        if (parseInt(points, 10) === '0' || parseInt(points, 10) === 0 || parseInt(points, 10) < 1) {
            requiredErrors.points = "Value cannot be 0";
        }
        if (!questionid) {
            requiredErrors.questionid = "Please Submit question and Answer!";
        }
        if (questionid?.length === 0) {
            requiredErrors.questionid = "Please Submit question and Answer!";
            return false;
        }

        if (!details.level) {
            tempDetailsError.level = 'Please select a level';
            isValid = false;
        }
        setDetailsError(tempDetailsError);
        setRequiredErrors(requiredErrors);
        return Object.values(requiredErrors).every((error) => error === "");
    };

    const CreateQuestion = (e) => {
        setSearch(e.target.value)
        // setQuestionid(e.target.value.id)
        setQuestionDetails(e.target.value)
        setVisible(true);

    }

    const SubmitQuestion = async () => {
        const questionTest = handleQuestionSubmit()
        if (questionTest === true) {
            setLoadingnew(true);
            try {
                const keysData = new FormData();
                keysData.append('level', details.level);
                keysData.append('questionType', details.questionType);
                keysData.append('cognitive_skills', selectedSkills);
                keysData.append('explanation', explanation);
                if (details.questionType === 'English') {
                    const mcqdata = {
                        question: satvalues.question,
                        mcqOptions: satvalues.mcqOptions.map(option => option.option),
                        correctAnswer: satvalues.mcqOptions.map(option => option.isCorrect),
                    };
                    keysData.append('question_text', satvalues.question);
                    keysData.append('mcqData', JSON.stringify(mcqdata));
                    keysData.append('passage', passage);
                }
                if (details.questionType === 'Maths') {
                    const mcqdata = {
                        question: mathsvalues.question,
                        mcqOptions: mathsvalues.mcqOptions.map(option => option.option),
                        correctAnswer: mathsvalues.mcqOptions.map(option => option.isCorrect),
                    };
                    keysData.append('question_text', mathsvalues.question);
                    keysData.append('mcqData', JSON.stringify(mcqdata));

                }

                const response = await adminServices.createSatQuestion(keysData);
                if (response.ok) {
                    setSubmitted(true);
                    setSnackbarTitle('Sat Questions created successfully');
                    setQuestionid(response.data.id?.id);
                    setSelectedQuestions((prev) => [...prev, response.data.id]);
                    setQuestionDetails(response.data.id?.question_text)
                    setQuestionDetails(response.data.id?.question_text)
                    setOpenSnackbar(true);
                    setLoadingnew(false);

                } else {
                    setLoadingnew(false);
                    console.error("Error:", response);
                }
            } catch (error) {
                console.error("An error occurred:", error);
            } finally {
                setSubmitQuestionClicked(true);
                setLoadingnew(false);
            }
        }
    }



    const handleModuleSubmitCreate = () => {
        const Validation = QuestionValidation()

        if (Validation) {
            const newFormObject = {
                name: moduleName,
                // questions_list: Array.isArray(questionid) ? questionid : [questionid],
                questions_list: selectedQuestions.map(item => item.id),
                weightage: points,
                type: details.questionType,
                questiondetails: selectedQuestions.map(item => item.question_text)
                // questiondetails : Array.isArray(selectedQuestions)
                // ? selectedQuestions.map(q => q.id.question_text)
                // : [selectedQuestions.id.question_text]
            };
            setModuleData([...moduleData, newFormObject]);
            setModuleName('');
            setPoints("")
            setQuestionid('');
            setSubmitted(false);
            setVisible(true);
            setQuestionDetails('')
            setSelectedQuestions([])

            setSelectedOption('create')
            setDetails({ level: "", questionType: "English" });
            setSelectedSkills("");
            setPage(0);
            setPassage("");
            setMathsValues({
                question: '',
                questionType: 'Maths',
                mcqOptions: [{ option: '', isCorrect: false }],
            }); setTimeout(() => {

                setSatValues({
                    question: '',
                    questionType: 'English',
                    mcqOptions: [{ option: '', isCorrect: false }],
                });
                setMathsValues({
                    question: '',
                    questionType: 'Maths',
                    mcqOptions: [{ option: '', isCorrect: false }],
                });
            }, 0);

            setSelectedSkills("");
            handleClose();
        }
    };
    const handleModuleSubmitCreateQuestion = (selectIndex) => {
        if (questionid && selectedSkills && submitQuestionClicked) {
            setSubmitQuestionClicked(false);
            const updatedModuleData = moduleData?.map((module, index) => {
                if (index === selectIndex) {
                    return {
                        ...module,
                        questions_list: Array.from(new Set([...module.questions_list, ...selectedQuestions.map(item => item.id)])),
                        // questiondetails: [...module.questiondetails, ...selectedQuestions.map(item => item.question_text)],
                    };
                }
                return module;
            });
            setModuleData(updatedModuleData);
            setModuleName('');
            setSubmitted(false);
            setPoints('');
            setQuestionid('');
            setQuestionDetails('')
            setVisible(true);
            setSelectedOption('create')

            setSatValues({})
            setMathsValues({})
            setTimeout(() => {


                setSatValues({
                    question: '',
                    questionType: 'English',
                    mcqOptions: [{ option: '', isCorrect: false }],
                });
                setMathsValues({
                    question: '',
                    questionType: 'Maths',
                    mcqOptions: [{ option: '', isCorrect: false }],
                });
            }, 0);

            setSelectedSkills('');
            handleCloseNew();
        }
        else {
            setRequiredErrors({
                questionid: "Please Submit question and Answer!",
            })
            if (!selectedSkills) {
                setQuestionError({ Objective: "Please Select Level of Objective" });
            }
        }
    };

    let buttonText = "Submit Question";
    if (loadingnew) {
        buttonText = "Submitting...";
    } else if (submitted) {
        buttonText = "Submitted";
    }

    const handleChangeOption = (e) => {
        setSelectedOption(e.target.value)
        if (e.target.value === 'select') {
            setSubmitQuestionClicked(true);
        }
        setRequiredErrors({
            moduleName: "",
            points: "",
            questionid: "",
        });
    }


    const handleAddQuestion = (question) => {
        setSelectedQuestions((prev) => {
            if (prev.find(q => q.question_id === question.question_id)) return prev;
            setQuestionDetails(question.question_text)
            setSelectedSkills(question?.name ? question?.name : '')
            setQuestionid((prev) => [...prev, question.question_id]);
            return [...prev, question];
        });
    };


    const handleDeselect = (id) => {
        setSelectedQuestions((prev) => prev.filter((q) => q.question_id !== id));
        // setSelectedSkills(question?.name?question?.name:'')
        setQuestionid((prev) => prev.filter((q) => q !== id));
    };

    // Update Individual Question

    const handleMCQOptionPreviewEdit = (index, field, value) => {
        const updatedOptions = [...questionDetails.options];
        updatedOptions[index][field] = value;
        setQuestionDetails((prev) => ({
            ...prev,
            options: updatedOptions
        }));
    };

    const handleAddOptionPreviewEdit = () => {
        setQuestionDetails((prev) => ({
            ...prev,
            options: [...prev.options, { option: '', isCorrect: false }]
        }));
    };

    const handleRemoveOptionPreviewEdit = (index) => {
        const updatedOptions = [...questionDetails.options];
        updatedOptions.splice(index, 1);
        setQuestionDetails((prev) => ({
            ...prev,
            options: updatedOptions
        }));
    };


    const UpdateIndividualQuestion = async () => {
        setLoadingQuestionnew(true)
        const keysData = new FormData();
        keysData.append('level', questionDetails.level);
        keysData.append('questionType', questionDetails.question_type);
        keysData.append('cognitive_skills', questionDetails.cognitive_skill_id);
        keysData.append('explanation', questionDetails.justification);
        keysData.append('question', questionDetails.question_text);
        keysData.append('courseCategory', '');
        if (questionDetails.question_type === 'English') {
            const mcqdata = {
                question: questionDetails.question_text,
                mcqOptions: questionDetails.options.map(option => option.option),
                correctAnswer: questionDetails.options.map(option => option.isCorrect),
            };

            keysData.append('mcqData', JSON.stringify(mcqdata));
            keysData.append('passage', passage);

        }
        if (questionDetails.question_type === 'Maths') {
            const mcqdata = {
                question: questionDetails.question_text,
                mcqOptions: questionDetails.options.map(option => option.option),
                correctAnswer: questionDetails.options.map(option => option.isCorrect),
            };

            keysData.append('mcqData', JSON.stringify(mcqdata));

        }

        const response = await adminServices.UpdateSATQuestionsBasedOnId(editDetails.id, keysData);
        if (response.ok) {
            setLoadingQuestionnew(false)
            modelClose()
            updateQuestionText(editDetails.id, questionDetails.question_text);

            setSnackbarTitle('SAT  Questions Updated successfully');
            setQuestionDetails({
                justification: '',
                level: '',
                options: [],
                question_text: '',
                question_type: '',
                cognitive_skill_id: ''
            });
            setEditDetails('');
        }
        setLoadingQuestionnew(false)
        modelClose()

    }

    const updateQuestionText = (questionId, newQuestionText) => {
        setModuleData(prevModuleData => {
            const updatedData = prevModuleData?.length > 0 && prevModuleData?.map(module => {
                const index = module.questions_list.indexOf(questionId);
                if (index !== -1) {
                    return {
                        ...module,
                        questiondetails: module?.questiondetails?.map((text, i) =>
                            i === index ? newQuestionText : text
                        )
                    };
                }
                return module;
            });

            return updatedData;
        });
    };

    return (
        <>
            <Page title="Edit Sat Assessment">
                <PageHeader pageTitle="Edit Sat Assessment" submodule="submodule" />
                <Grid container spacing={2} className='GACognitivesection' sx={{ mb: 2, padding: '15px 20px' }}>
                    <Grid item xs={12} sm={3} sx={{ marginBottom: '18px', paddingRight: '18px' }}>
                        <TextField
                            variant="outlined"
                            inputProps={{ maxLength: 50 }}
                            fullWidth
                            id="addname"
                            label="Sat Name *"
                            type="search"
                            value={name}
                            onChange={(e) => { setName(e.target.value); setNameError(''); }}
                            sx={{
                                // bgcolor: "#f0f0f0",
                                borderRadius: 1,
                                height: 36,
                                '& .MuiInputBase-input': {
                                    fontSize: 14,
                                    padding: "8px 12px",

                                },
                            }}
                            error={!!nameError}
                        />
                        {nameError && <FormHelperText error>{nameError}</FormHelperText>}
                    </Grid>



                    <Grid item xs={12} sm={3} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                        <FormControl fullWidth variant="outlined" sx={{
                            // bgcolor: "#f0f0f0", 
                            borderRadius: 1
                        }}>
                            <InputLabel id="complexity-level-label">Difficulty Level *</InputLabel>
                            <Select
                                disabled={submitted}
                                labelId="complexity-level-label"
                                id="addnamecomplexity"
                                value={complexity || ""}
                                onChange={(e) => { setComplexity(e.target.value); setComplexityError("") }}
                                label="Difficulty Level"
                                error={!!ComplexityError}
                                sx={{
                                    // height: 36,
                                    '& .MuiSelect-select': {
                                        // fontSize: 14,
                                        padding: "8px 12px",

                                    },
                                }}
                            >
                                <MenuItem value="">Select Complexity</MenuItem>
                                <MenuItem value="easy">Beginner</MenuItem>
                                <MenuItem value="medium">Intermediate</MenuItem>
                                <MenuItem value="hard">Advanced</MenuItem>
                            </Select>
                            {ComplexityError && <FormHelperText error>{ComplexityError}</FormHelperText>}
                        </FormControl>
                    </Grid>


                    <Grid item xs={12} sm={6} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                        <InputLabel id="complexity-level-label">Sat Time</InputLabel>

                        <Grid container className='AssessmentTime' spacing={2}>

                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Hours</InputLabel>
                                    <Select
                                        name="hours"
                                        value={time.hours}
                                        onChange={handleChange}
                                        disabled
                                        // label="Hours"
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 158,
                                                }
                                            }
                                        }}
                                    >
                                        {hoursArray.map((hour) => (
                                            <MenuItem key={hour} value={hour}>{hour}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Minutes</InputLabel>
                                    <Select
                                        name="minutes"
                                        value={time.minutes}
                                        onChange={handleChange}
                                        disabled
                                        // label="Minutes"
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 158,
                                                }
                                            }
                                        }}
                                    >
                                        {minutesArray.map((minute) => (
                                            <MenuItem key={minute} value={minute}>{minute}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>

                            <Grid item xs={4}>
                                <FormControl fullWidth>
                                    <InputLabel>Seconds</InputLabel>
                                    <Select
                                        name="seconds"
                                        value={time.seconds}
                                        disabled
                                        onChange={handleChange}
                                        // label="Seconds"
                                        className="dropHours"
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 228,
                                                }
                                            }
                                        }}
                                    >
                                        {secondsArray.map((second) => (
                                            <MenuItem key={second} value={second}>{second}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                        </Grid>
                    </Grid>





                    <Grid className="unique" item xs={12} sm={6} sx={{ marginBottom: '0px', paddingRight: '18px' }}>
                        {thumbPreview === null ? (
                            <FormControl style={{ height: '100%' }}
                                required
                                component="fieldset"
                                color="primary"
                                variant="outlined"
                                fullWidth
                                name="thumbImage"
                            >
                                <Typography variant="subtitle1">Thumb Image* <span style={{
                                    fontSize: '12px',
                                    float: 'inline-end', paddingBottom: '0', marginBottom: '0', position: 'relative', top: '5px'
                                }}>required resolution (360X200)</span></Typography>
                                <DropzoneArea className="dropTextArea"
                                    acceptedFiles={['image/jpeg', 'image/png', 'image/bmp']}
                                    showPreviews={false}
                                    dropzoneText="Drag and Drop Image or Browse File"
                                    showPreviewsInDropzone={false}
                                    maxFileSize={300000000}
                                    filesLimit={1}
                                    showAlerts={false}
                                    styles={{
                                        height: '100%', minHeight: '100%',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'center'
                                    }}
                                    onChange={handleFileChange}
                                    useChipsForPreview
                                    previewGridProps={{ container: { spacing: 1, direction: 'row' } }}
                                    showFileNamesInPreview
                                />
                                {ImageError && <FormHelperText error>{ImageError}</FormHelperText>}
                            </FormControl>


                        ) : (
                            <div className={classes.imgPreviewRoot}>
                                <Typography variant="subtitle1">Thumb Image</Typography>
                                <Badge
                                    badgeContent={
                                        <CloseIcon
                                            className={classes.badgeAlign}
                                            onClick={() => {
                                                setThumbPreview(null);
                                                setThumbImage(null);
                                            }}
                                        />
                                    }
                                >
                                    <Avatar
                                        variant="rounded"
                                        src={thumbPreview}
                                        style={{ minHeight: '150px !important' }}
                                        className={thumbPreview !== null && classes.fileImgSIze}
                                    />
                                </Badge>
                            </div>
                        )}
                    </Grid>


                    <Grid item xs={12} sm={6} sx={{ marginBottom: '15px', paddingRight: '18px', }} style={{ position: 'relative' }}>
                        <Typography variant="subtitle1">Sat Description*</Typography>
                        <ReactQuill
                            theme="snow"
                            id='questionText'
                            name="question"
                            value={editorValue}
                            onChange={(content) => {
                                if (content.length > 255) {
                                    handleChangeDescription(content.slice(0, 255));
                                } else {
                                    handleChangeDescription(content);
                                }
                            }}
                            onPaste={(e) => {
                                e.preventDefault();
                                const clipboardText = e.clipboardData.getData('text').slice(0, 255);
                                handleChangeDescription(clipboardText);
                            }}
                            // fullWidth
                            style={{ height: '150px', marginBottom: '30px' }}
                        />
                        {descriptionError && <FormHelperText style={{ position: 'absolute', bottom: '-40px' }} error>{descriptionError}</FormHelperText>}


                    </Grid>

                    <Grid item xs={6}>
                        <Typography className={classes.background} gutterBottom variant="subtitle1">
                            Short Description
                        </Typography>
                        <Box>
                            <textarea
                                id="shortdescription"
                                value={shortdescription}
                                onChange={(event) => setShortDescription(event.target.value)}
                                placeholder="Enter shortdescription"
                                rows="5"
                                cols="40"
                                maxLength={250}
                                style={{
                                    width: "100%",
                                    height: "150px",
                                    padding: "10px",
                                    fontSize: "16px",
                                    border: "1px solid #ccc",
                                    borderRadius: "4px",
                                    outline: "none",
                                    resize: "both",
                                    transition: "border-color 0.3s",
                                }}
                            />
                        </Box>
                    </Grid>


                    <Grid item xs={12} md={6} >
                        <span style={{ visibility: 'hidden' }}>Empty</span>
                    </Grid>




                    {moduleData && moduleData.length > 0 && moduleData.map((data, index) => (
                        <Grid container spacing={2} key={index} sx={{ marginBottom: 2 }} style={{ paddingLeft: '15px' }}>
                            <Grid item xs={6} style={{ paddingLeft: '15px' }}>
                                <Card>
                                    <CardActionArea>
                                        <CardContent>
                                            <Typography gutterBottom variant="h5" component="div">
                                                {data.name}
                                            </Typography>
                                            <Box display="flex" style={{ alignItems: 'center', justifyContent: 'space-between' }} >
                                                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                                    {data.weightage}%
                                                </Typography>
                                                <Box display="flex" sx={{ marginLeft: -1, alignItems: 'center' }}>

                                                    <IconButton id='AddCircleIcon' onClick={() => handleAddNewQuestion(index, data)} color="error">
                                                        <AddCircleIcon />
                                                    </IconButton>
                                                    <IconButton id='EditIcon' onClick={() => handleEditDetails(data, index)} color="error">
                                                        <EditIcon />
                                                    </IconButton>
                                                    <IconButton id='VisibilityIcon' style={{ height: '40px' }} onClick={() => handlePreviewOpen(data, index)} color="error">
                                                        < VisibilityIcon />
                                                    </IconButton>
                                                </Box>
                                                <Button id='btnUpdateIs' onClick={() => UpdateModules(data, index)} color="primary">
                                                    Update
                                                </Button>

                                                <Button id='btnUpdateIs' onClick={() => handleDelete(data, index)} color="primary">
                                                    Delete
                                                </Button>

                                            </Box>
                                        </CardContent>
                                    </CardActionArea>
                                </Card>
                            </Grid>
                        </Grid>
                    ))}


                    <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'end', paddingTop: '15px' }}>
                        <Button id="AddEnglish" sx={{ marginRight: '15px', paddingBottom: '0px' }} variant="contained" color="primary" onClick={() => handleDialogOpen("English")}>
                            Add English Module
                        </Button>
                        <Button id='AddMaths' sx={{ marginRight: '15px', paddingBottom: '0px' }} variant="contained" color="primary" onClick={() => handleDialogOpen("Maths")}>
                            Add Maths Module
                        </Button>

                        <Box sx={{ marginRight: '15px' }}>
                            {/* {selectedQuestions.length > 0 && ( */}
                            <LoadingButton
                                type="submit"
                                id="addassessmentgeneral"
                                onClick={handleUpdate}
                                variant="contained"
                                color="primary"
                                // fullWidth

                                loading={loading}
                            >
                                Update
                            </LoadingButton>
                            {/* )} */}
                        </Box>
                    </Grid>
                    {errorMessage && (
                        <Alert severity="error" sx={{ marginBottom: '10px' }}>
                            {errorMessage}
                        </Alert>
                    )}
                    {errorMessageAll && (
                        <Alert severity="error" sx={{ marginBottom: '10px' }}>
                            {errorMessageAll}
                        </Alert>)}
                </Grid>



                <Dialog open={open} onClose={handleClose} fullWidth
                    sx={{
                        '& .MuiDialog-paper': {
                            maxHeight: '75vh !important',
                            overflow: 'hidden !important'
                        }
                    }}
                >
                    <DialogTitle style={{ paddingBottom: '0px' }}>Add {details?.questionType === 'English' ? 'English Module' : 'Maths Module'}</DialogTitle>
                    <DialogContent className='GACognitivesection' sx={{ paddingTop: '25px !important' }}>
                        <Box sx={{ marginBottom: '18px' }}>
                            <TextField
                                variant="outlined"
                                inputProps={{ maxLength: 50 }}
                                fullWidth
                                id="addname"
                                label="Module Name *"
                                type="search"
                                value={moduleName}
                                onChange={(e) => { setModuleName(e.target.value); ClearError() }}
                                sx={{
                                    // bgcolor: "#f0f0f0",
                                    borderRadius: 1,

                                    height: 36,
                                    '& .MuiInputBase-input': {
                                        fontSize: 14,
                                        padding: "8px 12px",
                                    },

                                }}
                            />
                            {requiredErrors.moduleName && (
                                <FormHelperText error>{requiredErrors.moduleName}</FormHelperText>
                            )}
                        </Box>
                        <Box sx={{ marginBottom: '15px' }}>
                            <TextField
                                variant="outlined"
                                inputProps={{ maxLength: 50, inputMode: 'numeric', pattern: '[0-9]*' }}
                                fullWidth
                                id="addweight"
                                label="Module Weight *"
                                type="number"
                                value={points}
                                onChange={handlePoints}
                                sx={{
                                    // marginBottom: '20px',
                                    borderRadius: 1,

                                    height: 36,
                                    '& .MuiInputBase-input': {
                                        fontSize: 14,
                                        padding: "8px 12px",
                                    },
                                }}
                            />
                            {requiredErrors.points && (
                                <FormHelperText style={{ marginBottom: '5px' }} error>{requiredErrors.points}</FormHelperText>
                            )}
                        </Box>
                        {errorMessage && (
                            <Alert severity="error" sx={{ marginBottom: '10px' }}>
                                {errorMessage}
                            </Alert>
                        )}

                        <FormControl component="fieldset">
                            <RadioGroup
                                row
                                value={selectedOption}
                                onChange={(e) => handleChangeOption(e)}
                            >

                                <FormControlLabel value="create" id={`createRadio${details?.questionType}`} control={<Radio />} sx={{ marginRight: '40px' }} label="Create Question" />
                                <FormControlLabel value="select" id={`radioSelect${details?.questionType}`} control={<Radio />} label="Select Question" />
                            </RadioGroup>
                        </FormControl>

                        {selectedOption === 'select' ? (
                            <Box display="flex" flexDirection="column" gap={2}>
                                <Box style={{ marginTop: "10px" }}>
                                    <Grid container spacing={2}>


                                        <Grid item xs={6}>
                                            <FormControl style={{ display: "block", width: "100%" }} className={classes.formControl}
                                            // error={touched.questionType && Boolean(errors.questionType)}
                                            >
                                                <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                                                <Select name="level" labelId="demo-simple-select-standard-label"
                                                    disabled={submitted}
                                                    style={{ width: "100%" }}
                                                    id="level"
                                                    label="Level"
                                                    value={details.level}
                                                    onChange={(e) => setDetails(prevDetails => ({
                                                        ...prevDetails,
                                                        level: e.target.value
                                                    }))}
                                                    displayEmpty
                                                >
                                                    <MenuItem value="easy">Easy</MenuItem>
                                                    <MenuItem value="medium">Medium</MenuItem>
                                                    <MenuItem value="complex">Complex</MenuItem>
                                                </Select>

                                                {detailsError.level && (
                                                    <FormHelperText error>{detailsError.level}</FormHelperText>
                                                )}



                                            </FormControl>
                                        </Grid>


                                        <Grid item>
                                            <FormControl fullWidth variant="outlined">

                                                <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                                    Select Level of Objective</Typography>
                                                <FormGroup className='FormCheck'>
                                                    {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                        <FormControlLabel
                                                            key={skill}
                                                            id={`Label${skill}`}
                                                            control={
                                                                <Radio
                                                                    id={skill}
                                                                    checked={selectedSkills === skill}
                                                                    onChange={handleChangeCheck}
                                                                    value={skill}
                                                                />
                                                            }
                                                            label={skill}
                                                        />
                                                    ))}
                                                </FormGroup>
                                                {questionError && questionError.Objective && <FormHelperText error  >{questionError?.Objective}</FormHelperText >}

                                            </FormControl>
                                        </Grid>

                                        {details?.questionType === 'English' ?
                                            <>
                                                <div style={{ paddingLeft: "15px", width: "100%" }}>

                                                    <Grid item xs={12}>
                                                        <div className="search-select-container" style={{ display: 'flex', flexDirection: "column" }}>
                                                            <div className="search-select-container" style={{ display: 'flex', }}>
                                                                {page > 0 && <IconButton aria-label="Previous" onClick={handleLeftArrowClick}>
                                                                    <KeyboardDoubleArrowLeftIcon />
                                                                </IconButton>}
                                                                <input
                                                                    type="text"
                                                                    readOnly={submitted}
                                                                    onChange={CreateQuestion}
                                                                    placeholder="Search or Select"
                                                                    aria-label="Search or select an option"
                                                                    style={{ flex: 1, padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
                                                                />
                                                                <IconButton aria-label="Next" disabled={questionList?.length === 0} onClick={handleRightArrowClick}>
                                                                    <KeyboardDoubleArrowRightIcon />
                                                                </IconButton>
                                                            </div>
                                                            {loadingQuestion &&
                                                                <Typography style={{ justifyContent: 'center', alignItems: 'center', fontSize: '20' }}>
                                                                    Loading.....
                                                                </Typography>
                                                            }
                                                            {visible && !loadingQuestion && (
                                                                <ul className="dropdown" role="listbox" aria-expanded={query.length > 0}>
                                                                    {questionList && questionList?.length > 0 ? (
                                                                        questionList.map((item) => {                                                                            
                                                                            const sanitizedQuestion = item.question_text
                                                                                .replace(/<p>/g, '<span style="display: flex;">')
                                                                                .replace(/<\/p>/g, '</span>')
                                                                                .replace(/&nbsp;/g, ' '); const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            const isSelected = Array.isArray(questionid) ? questionid.some(q => q === item.question_id) : questionid === item.question_id;
                                                                            

                                                                            return (
                                                                                <li
                                                                                    key={item.question_id}
                                                                                    role="option"
                                                                                    className="dropdown-item"
                                                                                    tabIndex={0}
                                                                                    aria-selected={selectedOptionnew?.question_id === item.question_id}
                                                                                    title={sanitizedPassage}
                                                                                    style={{
                                                                                        backgroundColor: isSelected ? '#e0ffe0' : 'transparent',
                                                                                        padding: '8px',
                                                                                        borderRadius: '4px',
                                                                                        marginBottom: '4px',
                                                                                    }}
                                                                                >
                                                                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                                        <div id='searchQuestionsTag' dangerouslySetInnerHTML={{ __html: sanitizedQuestion }} />


                                                                                        {!isSelected && (
                                                                                            <Button
                                                                                                id="questionadd"
                                                                                                variant="outlined"
                                                                                                color="primary"
                                                                                                onClick={(e) => {
                                                                                                    e.stopPropagation();
                                                                                                    handleAddQuestion(item);
                                                                                                }}
                                                                                                sx={{
                                                                                                    fontSize: '0.75rem',
                                                                                                    minWidth: '24px',
                                                                                                    minHeight: '24px',
                                                                                                    padding: '2px',
                                                                                                    borderRadius: '12px',
                                                                                                }}
                                                                                            >
                                                                                                +
                                                                                            </Button>
                                                                                        )}
                                                                                    </div>
                                                                                </li>
                                                                            );
                                                                        })
                                                                    ) : (
                                                                        <li className="dropdown-item" role="option">
                                                                            No results found
                                                                        </li>
                                                                    )}

                                                                </ul>

                                                            )}
                                                            {
                                                                !loadingQuestion && selectedQuestions && selectedQuestions.length > 0 && (
                                                                    <>
                                                                        <Typography>Selected Questions:</Typography>
                                                                        {selectedQuestions.map((item, index) => {
                                                                            const sanitizedQuestion = item.question_text
                                                                                .replace(/<p>/g, '<span style="display: flex;">')
                                                                                .replace(/<\/p>/g, '</span>')
                                                                                .replace(/&nbsp;/g, ' ');



                                                                            const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            return (
                                                                                <Tooltip
                                                                                    key={index}
                                                                                    title={sanitizedPassage}
                                                                                    placement="top"
                                                                                >
                                                                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                                        <Typography
                                                                                            id="QuestionArea"
                                                                                            dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                                                                        />
                                                                                        <Button onClick={() => handleDeselect(item.question_id)}>
                                                                                            x
                                                                                        </Button>
                                                                                    </div>
                                                                                </Tooltip>
                                                                            );
                                                                        }
                                                                        )}

                                                                    </>
                                                                )
                                                            }

                                                        </div>
                                                    </Grid>

                                                </div>
                                            </>
                                            :
                                            <>
                                                <div style={{ paddingLeft: "15px", width: "100%" }}>

                                                    <Grid item xs={12}>
                                                        <div className="search-select-container" style={{ display: 'flex', flexDirection: "column" }}>
                                                            <div className="search-select-container" style={{ display: 'flex', }}>
                                                                {page > 0 && <IconButton aria-label="Previous" onClick={handleLeftArrowClick}>
                                                                    <KeyboardDoubleArrowLeftIcon />
                                                                </IconButton>}
                                                                <input
                                                                    type="text"
                                                                    readOnly={submitted}
                                                                    onChange={CreateQuestion}
                                                                    placeholder="Search or Select"
                                                                    aria-label="Search or select an option"
                                                                    style={{ flex: 1, padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
                                                                />
                                                                <IconButton aria-label="Next" disabled={questionList?.length === 0}  onClick={handleRightArrowClick}>
                                                                    <KeyboardDoubleArrowRightIcon />
                                                                </IconButton>
                                                            </div>
                                                            {loadingQuestion &&
                                                                <Typography style={{ justifyContent: 'center', alignItems: 'center', fontSize: '20' }}>
                                                                    Loading.....
                                                                </Typography>
                                                            }
                                                            {visible && !loadingQuestion && (
                                                                <ul className="dropdown" role="listbox" aria-expanded={query.length > 0}>
                                                                    {questionList && questionList?.length > 0 ? (
                                                                        questionList.map((item) => {
                                                                            const sanitizedQuestion = item.question_text
                                                                                .replace(/<p>/g, '<span style="display: flex;">')
                                                                                .replace(/<\/p>/g, '</span>')
                                                                                .replace(/&nbsp;/g, ' ');
                                                                            const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            const isSelected = Array.isArray(questionid) ? questionid.some(q => q === item.question_id) : questionid === item.question_id;

                                                                            return (
                                                                                <li
                                                                                    key={item.question_id}
                                                                                    role="option"
                                                                                    className="dropdown-item"
                                                                                    tabIndex={0}
                                                                                    aria-selected={selectedOptionnew?.question_id === item.question_id}
                                                                                    title={sanitizedPassage}
                                                                                    style={{
                                                                                        backgroundColor: isSelected ? '#e0ffe0' : 'transparent',
                                                                                        padding: '8px',
                                                                                        borderRadius: '4px',
                                                                                        marginBottom: '4px',
                                                                                    }}
                                                                                >
                                                                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                                        <div id='searchQuestionsTag' dangerouslySetInnerHTML={{ __html: sanitizedQuestion }} />


                                                                                        {!isSelected && (
                                                                                            <Button
                                                                                                id="questionadd"
                                                                                                variant="outlined"
                                                                                                color="primary"
                                                                                                onClick={(e) => {
                                                                                                    e.stopPropagation();
                                                                                                    handleAddQuestion(item);
                                                                                                }}
                                                                                                sx={{
                                                                                                    fontSize: '0.75rem',
                                                                                                    minWidth: '24px',
                                                                                                    minHeight: '24px',
                                                                                                    padding: '2px',
                                                                                                    borderRadius: '12px',
                                                                                                }}
                                                                                            >
                                                                                                +
                                                                                            </Button>
                                                                                        )}
                                                                                    </div>
                                                                                </li>
                                                                            );
                                                                        })
                                                                    ) : (
                                                                        <li className="dropdown-item" role="option">
                                                                            No results found
                                                                        </li>
                                                                    )}

                                                                </ul>

                                                            )}

                                                            {
                                                                !loadingQuestion && selectedQuestions && selectedQuestions.length > 0 && (
                                                                    <>
                                                                        <Typography>Selected Questions:</Typography>
                                                                        {selectedQuestions.map((item, index) => {
                                                                            const sanitizedQuestion = item.question_text
                                                                                .replace(/<p>/g, '<span style="display: flex;">')
                                                                                .replace(/<\/p>/g, '</span>')
                                                                                .replace(/&nbsp;/g, ' ');


                                                                            const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                                            return (
                                                                                <Tooltip
                                                                                    key={index}
                                                                                    title={sanitizedPassage}
                                                                                    placement="top"
                                                                                >
                                                                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                                        <Typography
                                                                                            id="QuestionArea"
                                                                                            dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                                                                        />
                                                                                        <Button onClick={() => handleDeselect(item.question_id)}>
                                                                                            x
                                                                                        </Button>
                                                                                    </div>
                                                                                </Tooltip>
                                                                            );
                                                                        }
                                                                        )}

                                                                    </>
                                                                )
                                                            }
                                                        </div>
                                                    </Grid>

                                                </div>
                                            </>
                                        }

                                        {selectedOption === 'create' &&
                                            <Grid item xs={12}>
                                                <LoadingButton
                                                    id="subMitButton"
                                                    type="submit"
                                                    variant="contained"
                                                    color="primary"
                                                    onClick={CreateQuestion}
                                                    loading={loadingnew}
                                                    fullWidth
                                                >
                                                    Submit
                                                </LoadingButton>
                                            </Grid>}
                                    </Grid>
                                </Box>

                            </Box>
                        ) : (

                            <Box display="flex" flexDirection="column" style={{ marginTop: "10px" }} gap={2}>
                                <Grid container spacing={2}>
                                    <Grid item xs={6} fullWidth >
                                        <FormControl className={classes.formControl}
                                            style={{ width: "100%" }}
                                        // error={touched.questionType && Boolean(errors.questionType)}
                                        >
                                            <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                                            <Select
                                                disabled={submitted}
                                                name="level"
                                                labelId="demo-simple-select-standard-label"
                                                id="level"
                                                label="Level"
                                                value={details.level}
                                                onChange={(e) => setDetails(prevDetails => ({
                                                    ...prevDetails,
                                                    level: e.target.value
                                                }))}
                                                displayEmpty
                                            >
                                                <MenuItem value="easy">Easy</MenuItem>
                                                <MenuItem value="medium">Medium</MenuItem>
                                                <MenuItem value="complex">Complex</MenuItem>
                                            </Select>
                                            {detailsError.level && (
                                                <FormHelperText error>{detailsError.level}</FormHelperText>
                                            )}


                                        </FormControl>
                                    </Grid>


                                    <Grid item>
                                        <FormControl fullWidth variant="outlined">

                                            <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                                Select Level of Objective</Typography>
                                            <FormGroup className='FormCheck'>
                                                {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                    <FormControlLabel
                                                        key={skill}
                                                        id={`Label${skill}`}
                                                        control={
                                                            <Radio
                                                                id={skill}
                                                                checked={selectedSkills === skill}
                                                                onChange={handleChangeCheck}
                                                                value={skill}
                                                            />
                                                        }
                                                        label={skill}
                                                    />
                                                ))}
                                            </FormGroup>
                                            {questionError && questionError.Objective && <FormHelperText error  >{questionError?.Objective}</FormHelperText >}

                                        </FormControl>
                                    </Grid>

                                    {details?.questionType === 'English' ?
                                        <>
                                            <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                                <Typography variant="subtitle1">Passage *</Typography>
                                                <ReactQuill
                                                    readOnly={submitted}
                                                    theme="snow"
                                                    id='questionText'
                                                    name="question"
                                                    modules={modules}
                                                    formats={formats}
                                                    defaultValue={passage}
                                                    onChange={(content) => {
                                                        setPassage(content);
                                                        setQuestionError({
                                                            question: ''
                                                        });
                                                    }}
                                                />
                                                {questionError && questionError.passage && <FormHelperText error  >{questionError?.passage}</FormHelperText >}
                                            </Grid>


                                            {details?.questionType === 'English' &&
                                                <>
                                                    <div style={{ paddingLeft: "15px" }}>
                                                        <Grid item xs={12}>
                                                            <FormControl className={classes.formControl}>
                                                                <Typography className={classes.background} gutterBottom variant="subtitle1">
                                                                    Create Question*
                                                                </Typography>

                                                                <ReactQuill
                                                                    readOnly={submitted}
                                                                    theme="snow"
                                                                    id={`questionText`}
                                                                    name="question"
                                                                    defaultValue={satvalues.question}
                                                                    onChange={(value) => handleChangeQuestionEnglish('question', value)}
                                                                    modules={modules}
                                                                    formats={formats}
                                                                    fullWidth
                                                                />
                                                            </FormControl>
                                                            {questionError && questionError.question && <FormHelperText error >{questionError?.question}</FormHelperText>}

                                                        </Grid>


                                                        {satvalues?.mcqOptions?.map((opt, index) => (

                                                            <div key={opt.id} style={{ position: 'relative', marginTop: '10px' }}>
                                                                <Grid container spacing={2} alignItems="center">
                                                                    <Grid item xs={12} style={{ display: 'flex', alignItems: 'end', marginLeft: 40 }}>
                                                                        <ReactQuill
                                                                            readOnly={submitted}
                                                                            theme="snow"
                                                                            id={`optiontext`}
                                                                            name={`mcqQuestion`}
                                                                            value={opt.option}
                                                                            onChange={(value) => { handleMCQOptionChangeEnglish(index, 'option', value); setQuestionError({ option: '', }) }}
                                                                            modules={modules}
                                                                            formats={formats}
                                                                            placeholder="Option"
                                                                            style={{ marginTop: 10, flex: 1 }}
                                                                        />
                                                                        <IconButton
                                                                            disabled={submitted}
                                                                            id='removeOption'
                                                                            aria-label="delete"
                                                                            color="error"
                                                                            onClick={() => handleRemoveOptionEnglish(index)}
                                                                            style={{ marginLeft: '-8px', marginTop: '-8px' }}
                                                                        >
                                                                            <ClearIcon fontSize="small" />
                                                                        </IconButton>
                                                                        <FormControlLabel
                                                                            control={
                                                                                <Checkbox
                                                                                    id='checkBoxMCQ'
                                                                                    name={`mcqOptionsisCorrect`}
                                                                                    checked={opt.isCorrect}
                                                                                    onChange={() => handleMCQOptionChangeEnglish(index, 'isCorrect', !opt.isCorrect)}
                                                                                    disabled={!opt.option.trim() || submitted}
                                                                                />
                                                                            }
                                                                            label="Correct"
                                                                        />
                                                                    </Grid>
                                                                </Grid>
                                                            </div>

                                                        ))}
                                                        {questionError && questionError.option && <FormHelperText error >{questionError?.option}</FormHelperText>}
                                                        {questionError && questionError.correctAnswer && <FormHelperText error >{questionError?.correctAnswer}</FormHelperText>}

                                                        <Button
                                                            disabled={submitted}
                                                            variant="contained"
                                                            color="primary"
                                                            id='Addoptions'
                                                            onClick={handleAddOptionEnglish}
                                                            style={{ width: '120px', backgroundColor: 'rgb(63, 186, 150)', marginTop: '10px', borderRadius: '6px' }}
                                                        >
                                                            Add Option
                                                        </Button>
                                                        {requiredErrors.questionid && (
                                                            <FormHelperText error>{requiredErrors.questionid}</FormHelperText>
                                                        )}
                                                    </div>
                                                    <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                                        <Typography variant="subtitle1">Explanation *</Typography>
                                                        <ReactQuill
                                                            readOnly={submitted}
                                                            theme="snow"
                                                            id="explanation"
                                                            name="explanation"
                                                            modules={modules}
                                                            formats={formats}
                                                            defaultValue={explanation}
                                                            onChange={(content) => {
                                                                satExplanation(content);
                                                                setQuestionError({
                                                                    explanation: ''
                                                                });
                                                            }}
                                                        />
                                                        {questionError && questionError.explanation && <FormHelperText error  >{questionError?.explanation}</FormHelperText >}
                                                    </Grid>
                                                </>

                                            }




                                        </>
                                        :
                                        <>
                                            <Grid item xs={12}>
                                                <FormControl style={{ width: '100%' }} className={classes.formControl}
                                                //  error={touched.question && Boolean(errors.question)}
                                                >
                                                    <Typography className={classes.background} color="primary" gutterBottom variant="subtitle1">
                                                        Create Question*
                                                    </Typography>
                                                    <ReactQuill
                                                        readOnly={submitted}
                                                        theme="snow"
                                                        id="questionText"
                                                        name="question"
                                                        defaultValue={mathsvalues.question}
                                                        onChange={(value) => handleChangeMathsQuestion('question', value)}
                                                        modules={modules}
                                                        formats={formats}
                                                        // onBlur={() => setTouched((prev) => ({ ...prev, question: true }))}
                                                        fullWidth
                                                    />
                                                </FormControl>
                                                {questionError && questionError.question && <FormHelperText error>{questionError?.question}</FormHelperText >}

                                            </Grid>


                                            {mathsvalues?.questionType === 'Maths' && (
                                                <>
                                                    <Grid item xs={12}>
                                                        {mathsvalues?.mcqOptions.map((opt, index) => (
                                                            <div key={opt.id} style={{ position: 'relative', marginTop: '10px' }}>
                                                                <Grid container spacing={2} alignItems="center">
                                                                    <Grid item xs={12} style={{ display: 'flex', alignItems: 'end', marginLeft: 40 }}>
                                                                        <ReactQuill
                                                                            readOnly={submitted}
                                                                            theme="snow"
                                                                            id={`optiontext-${index}`}

                                                                            name={`mcqQuestion-${index}`}
                                                                            value={opt.option}
                                                                            onChange={(value) => { handleMCQOptionChangeMaths(index, 'option', value); setQuestionError({ option: '', }) }}
                                                                            modules={modules}
                                                                            formats={formats}
                                                                            placeholder="Option"
                                                                            style={{ marginTop: 10, flex: 1 }}
                                                                        />
                                                                        <IconButton
                                                                            disabled={submitted}
                                                                            aria-label="delete"
                                                                            color="error"
                                                                            onClick={() => handleRemoveOptionMaths(index)}
                                                                            style={{ marginLeft: '-8px', marginTop: '-8px' }}
                                                                        >
                                                                            <ClearIcon fontSize="small" />
                                                                        </IconButton>
                                                                        <FormControlLabel
                                                                            control={
                                                                                <Checkbox
                                                                                    name={`mcqOptions.${index}.isCorrect`}
                                                                                    checked={opt.isCorrect}
                                                                                    onChange={() => handleMCQOptionChangeMaths(index, 'isCorrect', !opt.isCorrect)}
                                                                                    disabled={!opt.option.trim() || submitted}
                                                                                />
                                                                            }
                                                                            label="Correct"
                                                                        />
                                                                    </Grid>


                                                                </Grid>
                                                            </div>
                                                        ))}

                                                        {submitError && (
                                                            <div style={{ color: 'red', marginBottom: '10px', marginLeft: '100px', fontSize: '12px' }}>
                                                                {submitError}
                                                            </div>
                                                        )}

                                                        {questionError && questionError.option && <FormHelperText error >{questionError?.option}</FormHelperText>}
                                                        {questionError && questionError.correctAnswer && <FormHelperText error >{questionError?.correctAnswer}</FormHelperText>}

                                                        <Button
                                                            disabled={submitted}
                                                            variant="contained"
                                                            color="primary"
                                                            id='addedOptionIs'
                                                            onClick={handleAddOptionMaths}
                                                            style={{ width: '120px', backgroundColor: 'rgb(63, 186, 150)', marginTop: '10px', borderRadius: '6px' }}
                                                        >
                                                            Add Option
                                                        </Button>
                                                        {requiredErrors.questionid && (
                                                            <FormHelperText error>{requiredErrors.questionid}</FormHelperText>
                                                        )}
                                                    </Grid>
                                                    <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                                        <Typography variant="subtitle1">Explanation *</Typography>
                                                        <ReactQuill
                                                            readOnly={submitted}
                                                            theme="snow"
                                                            id="explanation"
                                                            name="explanation"
                                                            modules={modules}
                                                            formats={formats}
                                                            defaultValue={explanation}
                                                            onChange={(content) => {
                                                                satExplanation(content);
                                                                setQuestionError({
                                                                    explanation: ''
                                                                });
                                                            }}
                                                        />
                                                        {questionError && questionError.explanation && <FormHelperText error  >{questionError?.explanation}</FormHelperText >}
                                                    </Grid>
                                                </>
                                            )}
                                        </>
                                    }


                                    {submitted ? <Grid item xs={12}>
                                        <LoadingButton
                                            id="subMitButton"
                                            type="submit"
                                            variant="contained"
                                            color="primary"
                                            disabled
                                            loading={loadingnew}
                                            fullWidth
                                        >
                                            submitted
                                        </LoadingButton>
                                    </Grid> :
                                        <Grid item xs={12}>
                                            <LoadingButton
                                                id="subMitButton"
                                                type="submit"
                                                variant="contained"
                                                color="primary"
                                                // onClick={CreateQuestion}
                                                onClick={SubmitQuestion}
                                                loading={loadingnew}
                                                fullWidth
                                            >
                                                Submit Question
                                            </LoadingButton>
                                        </Grid>}
                                </Grid>
                            </Box>

                        )}
                    </DialogContent>


                    <DialogActions>
                        <Button id='modulepopSubmits' onClick={handleModuleSubmitCreate} color="secondary">
                            Submit
                        </Button>
                        <Button id='modulepopCancel' onClick={handleClose} color="primary">
                            Cancel
                        </Button>
                    </DialogActions>


                </Dialog>






                <Dialog open={openDialog} onClose={handleCloseNew} fullWidth>
                    <DialogTitle style={{ paddingBottom: '0px' }}>Add English Question</DialogTitle>
                    <DialogContent className='GACognitivesection' sx={{ paddingTop: '25px !important' }}>

                        <FormControl component="fieldset">
                            <RadioGroup
                                row
                                value={selectedOption}
                                onChange={(e) => handleChangeOption(e)}
                            >

                                <FormControlLabel value="create" control={<Radio />} sx={{ marginRight: '40px' }} label="Create Question" />
                                <FormControlLabel value="select" control={<Radio />} label="Select Question" />
                            </RadioGroup>
                        </FormControl>

                        {selectedOption === 'select' ? <Grid container spacing={2}>
                            <Grid item sm={12}>
                                <FormControl fullWidth>

                                    <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                        Select Level of Objective
                                    </Typography>
                                    <FormGroup className='FormCheck'>
                                        {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                            <FormControlLabel
                                                key={skill}
                                                id={`LabelIs${skill}`}
                                                control={
                                                    <Radio
                                                        id={skill}
                                                        checked={selectedSkills === skill}
                                                        onChange={handleChangeCheck}
                                                        value={skill}
                                                    />
                                                }
                                                label={skill}
                                            />
                                        ))}
                                    </FormGroup>
                                    {questionError && questionError.Objective && <FormHelperText error  >{questionError?.Objective}</FormHelperText >}

                                </FormControl>
                            </Grid>





                            <Grid item xs={12}>
                                <div className="search-select-container">
                                    <div className="search-select-container" style={{ display: 'flex', }}>
                                        {page > 0 && <IconButton aria-label="Previous" onClick={handleLeftArrowClick}>
                                            <KeyboardDoubleArrowLeftIcon />
                                        </IconButton>}
                                        <input
                                            type="text"
                                            readOnly={submitted}
                                            onChange={CreateQuestion}
                                            placeholder="Search or Select"
                                            aria-label="Search or select an option"
                                            style={{ flex: 1, padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
                                        />
                                        <IconButton aria-label="Next" disabled={questionList?.length === 0} onClick={handleRightArrowClick}>
                                            <KeyboardDoubleArrowRightIcon />
                                        </IconButton>
                                    </div>
                                    {loadingQuestion &&
                                        <Typography style={{ justifyContent: 'center', alignItems: 'center', fontSize: '20' }}>
                                            Loading.....
                                        </Typography>
                                    }

                                    {visible && !loadingQuestion && (
                                        <ul className="dropdown" role="listbox" aria-expanded={query.length > 0}>
                                            {questionList && questionList.length > 0 ? (
                                                questionList.map((item) => {
                                                    const sanitizedQuestion = item.question_text
                                                        .replace(/<p>/g, '<span style="display: flex;">')
                                                        .replace(/<\/p>/g, '</span>')
                                                        .replace(/&nbsp;/g, ' ');
                                                    const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');

                                                    const isSelected = Array.isArray(questionid) ? questionid.some(q => q === item.question_id) : questionid === item.question_id;

                                                    return (
                                                        <li
                                                            key={item.question_id}
                                                            role="option"
                                                            className="dropdown-item"
                                                            tabIndex={0}
                                                            aria-selected={selectedOptionnew?.question_id === item.question_id}
                                                            title={sanitizedPassage}
                                                            style={{
                                                                backgroundColor: isSelected ? '#e0ffe0' : 'transparent',
                                                                padding: '8px',
                                                                borderRadius: '4px',
                                                                marginBottom: '4px',
                                                            }}
                                                        >
                                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                <div id='searchQuestionsTag' dangerouslySetInnerHTML={{ __html: sanitizedQuestion }} />


                                                                {!isSelected && (
                                                                    <Button
                                                                        id="questionadd"
                                                                        variant="outlined"
                                                                        color="primary"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            handleAddQuestion(item);
                                                                        }}
                                                                        sx={{
                                                                            fontSize: '0.75rem',
                                                                            minWidth: '24px',
                                                                            minHeight: '24px',
                                                                            padding: '2px',
                                                                            borderRadius: '12px',
                                                                        }}
                                                                    >
                                                                        +
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </li>
                                                    );
                                                })
                                            ) : (
                                                <li className="dropdown-item" role="option">
                                                    No results found
                                                </li>
                                            )}

                                        </ul>
                                    )}
                                    {
                                        !loadingQuestion && selectedQuestions && selectedQuestions.length > 0 && (
                                            <>
                                                <Typography>Selected Questions:</Typography>
                                                {selectedQuestions.map((item, index) => {
                                                    const sanitizedQuestion = item.question_text
                                                        .replace(/<p>/g, '<span style="display: flex;">')
                                                        .replace(/<\/p>/g, '</span>')
                                                        .replace(/&nbsp;/g, ' ');


                                                    const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                    return (
                                                        <Tooltip
                                                            key={index}
                                                            title={sanitizedPassage}
                                                            placement="top"
                                                        >
                                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                <Typography
                                                                    id="QuestionArea"
                                                                    dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                                                />
                                                                <Button onClick={() => handleDeselect(item.question_id)}>
                                                                    x
                                                                </Button>
                                                            </div>
                                                        </Tooltip>
                                                    );
                                                }
                                                )}

                                            </>
                                        )
                                    }
                                </div>
                            </Grid>
                        </Grid>

                            :
                            <Grid container spacing={2}>
                                <Grid item sm={12}>
                                    <FormControl fullWidth>

                                        <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                            Select Level of Objective
                                        </Typography>
                                        <FormGroup className='FormCheck'>
                                            {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                <FormControlLabel
                                                    key={skill}
                                                    id={`Label${skill}`}
                                                    control={
                                                        <Radio
                                                            id={skill}
                                                            checked={selectedSkills === skill}
                                                            onChange={handleChangeCheck}
                                                            value={skill}
                                                        />
                                                    }
                                                    label={skill}
                                                />
                                            ))}
                                        </FormGroup>
                                        {questionError && questionError.Objective && <FormHelperText error  >{questionError?.Objective}</FormHelperText >}

                                    </FormControl>
                                </Grid>

                                <Grid item xs={6} fullWidth>
                                    <FormControl className={classes.formControl} style={{ width: "100%" }}
                                    // error={touched.questionType && Boolean(errors.questionType)}
                                    >
                                        <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                                        <Select
                                            disabled={submitted}
                                            name="level"
                                            labelId="demo-simple-select-standard-label"
                                            id="level"
                                            label="Level"
                                            value={details.level}
                                            onChange={(e) => setDetails(prevDetails => ({
                                                ...prevDetails,
                                                level: e.target.value
                                            }))}
                                            displayEmpty
                                        >
                                            <MenuItem value="easy">Easy</MenuItem>
                                            <MenuItem value="medium">Medium</MenuItem>
                                            <MenuItem value="complex">Complex</MenuItem>
                                        </Select>
                                        {detailsError?.level && (
                                            <FormHelperText error>{detailsError.level}</FormHelperText>
                                        )}
                                    </FormControl>
                                </Grid>

                                <>
                                    <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                        <Typography variant="subtitle1">Passage *</Typography>
                                        <ReactQuill
                                            readOnly={submitted}
                                            theme="snow"
                                            id='questionText'
                                            name="question"
                                            modules={modules}
                                            formats={formats}
                                            defaultValue={passage}
                                            onChange={(content) => {
                                                setPassage(content);
                                                setQuestionError({
                                                    question: ''
                                                });
                                            }}
                                        />
                                        {
                                            questionError?.passage &&
                                            (<FormHelperText error>{questionError?.passage}</FormHelperText>)
                                        }



                                    </Grid>


                                    {details?.questionType === 'English' &&
                                        <>                                        <div >
                                            <Grid item xs={12}>
                                                <FormControl className={classes.formControl}>
                                                    <Typography className={classes.background} gutterBottom variant="subtitle1">
                                                        Create Question*
                                                    </Typography>

                                                    <ReactQuill
                                                        readOnly={submitted}
                                                        theme="snow"
                                                        id={`questionText`}
                                                        name="question"
                                                        defaultValue={satvalues.question}
                                                        onChange={(value) => handleChangeQuestionEnglish('question', value)}
                                                        modules={modules}
                                                        formats={formats}
                                                        fullWidth
                                                    />
                                                    {questionError && questionError.question && <FormHelperText error>{questionError?.question}</FormHelperText>}

                                                </FormControl>
                                            </Grid>


                                            {satvalues?.mcqOptions?.map((opt, index) => (
                                                <div key={opt.id} style={{ position: 'relative', marginTop: '10px' }}>
                                                    <Grid container spacing={2} alignItems="center">
                                                        <Grid item xs={12} style={{ display: 'flex', alignItems: 'end', marginLeft: 40 }}>
                                                            <ReactQuill
                                                                readOnly={submitted}
                                                                theme="snow"
                                                                id={`optiontext`}
                                                                name={`mcqQuestion`}
                                                                value={opt.option}
                                                                onChange={(value) => { handleMCQOptionChangeEnglish(index, 'option', value); setQuestionError({ option: '', }) }}
                                                                modules={modules}
                                                                formats={formats}
                                                                placeholder="Option"
                                                                style={{ marginTop: 10, flex: 1 }}
                                                            />
                                                            <IconButton
                                                                disabled={submitted}
                                                                aria-label="delete"
                                                                color="error"
                                                                onClick={() => handleRemoveOptionEnglish(index)}
                                                                style={{ marginLeft: '-8px', marginTop: '-8px' }}
                                                            >
                                                                <ClearIcon fontSize="small" />
                                                            </IconButton>
                                                            <FormControlLabel
                                                                control={
                                                                    <Checkbox
                                                                        id='checkBoxMCQ'
                                                                        name={`mcqOptionsisCorrect`}
                                                                        checked={opt.isCorrect}
                                                                        onChange={() => handleMCQOptionChangeEnglish(index, 'isCorrect', !opt.isCorrect)}
                                                                        disabled={!opt.option.trim() || submitted}
                                                                    />
                                                                }
                                                                label="Correct"
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                </div>
                                                // )
                                            ))}
                                            {questionError && questionError.option && <FormHelperText error >{questionError?.option}</FormHelperText>}
                                            {questionError && questionError.correctAnswer && <FormHelperText error >{questionError?.correctAnswer}</FormHelperText>}

                                            <Button
                                                disabled={submitted}
                                                variant="contained"
                                                color="primary"
                                                id='AddOptionschk'
                                                onClick={handleAddOptionEnglish}
                                                style={{ width: '120px', backgroundColor: 'rgb(63, 186, 150)', marginTop: '10px', borderRadius: '6px' }}
                                            >
                                                Add Option
                                            </Button>
                                            {requiredErrors.questionid && (
                                                <FormHelperText error>{requiredErrors.questionid}</FormHelperText>
                                            )}
                                        </div>
                                            <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                                <Typography variant="subtitle1">Explanation *</Typography>
                                                <ReactQuill
                                                    readOnly={submitted}
                                                    theme="snow"
                                                    id="explanation"
                                                    name="explanation"
                                                    modules={modules}
                                                    formats={formats}
                                                    defaultValue={explanation}
                                                    onChange={(content) => {
                                                        satExplanation(content);
                                                        setQuestionError({
                                                            explanation: ''
                                                        });
                                                    }}
                                                />
                                                {questionError && questionError.explanation && <FormHelperText error  >{questionError?.explanation}</FormHelperText >}
                                            </Grid>
                                        </>
                                    }
                                </>


                                <Grid item xs={12}>
                                    <Button id={`btnsumbition${buttonText}`} onClick={SubmitQuestion} type="submit"
                                        disabled={loadingnew || submitted}
                                        variant="contained" color="primary" fullWidth>
                                        {buttonText}
                                    </Button>
                                </Grid>
                                {requiredErrors.questionid && (
                                    <FormHelperText error>{requiredErrors.questionid}</FormHelperText>
                                )}
                            </Grid>}
                    </DialogContent>


                    <DialogActions>
                        <Button id='ButtonIsSubmition' onClick={() => handleModuleSubmitCreateQuestion(selectIndex)} color="secondary">
                            Submit
                        </Button>
                        <Button id='ButtonIsCancel' onClick={handleCloseNew} color="primary">
                            Cancel
                        </Button>
                    </DialogActions>


                </Dialog>


                <Dialog open={OpenDialogNew} onClose={handleCloseMaths} fullWidth
                    sx={{
                        '& .MuiDialog-paper': {
                            maxHeight: '75vh !important',
                            overflow: 'hidden !important'
                        }
                    }}
                >
                    <DialogTitle style={{ paddingBottom: '0px' }}>Add Maths Question</DialogTitle>
                    <DialogContent className='GACognitivesection' sx={{ paddingTop: '25px !important' }}>

                        <FormControl component="fieldset">
                            <RadioGroup
                                row
                                value={selectedOption}
                                onChange={(e) => handleChangeOption(e)}
                            >

                                <FormControlLabel value="create" control={<Radio />} sx={{ marginRight: '40px' }} label="Create Question" />
                                <FormControlLabel value="select" control={<Radio />} label="Select Question" />
                            </RadioGroup>
                        </FormControl>

                        {selectedOption === 'select' ?
                            <Grid container spacing={2}>
                                <Grid item sm={12}>
                                    <FormControl fullWidth>

                                        <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                            Select Level of Objective
                                        </Typography>
                                        <FormGroup className='FormCheck'>
                                            {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                <FormControlLabel
                                                    key={skill}
                                                    id={`Label${skill}`}
                                                    control={
                                                        <Radio
                                                            id={skill}
                                                            checked={selectedSkills === skill}
                                                            onChange={handleChangeCheck}
                                                            value={skill}
                                                        />
                                                    }
                                                    label={skill}
                                                />
                                            ))}
                                        </FormGroup>
                                        {questionError && questionError.Objective && <FormHelperText error  >{questionError?.Objective}</FormHelperText >}

                                    </FormControl>
                                </Grid>


                                <Grid item xs={12}>
                                    <div className="search-select-container">
                                        <div className="search-select-container" style={{ display: 'flex', }}>
                                            {page > 0 && <IconButton aria-label="Previous" onClick={handleLeftArrowClick}>
                                                <KeyboardDoubleArrowLeftIcon />
                                            </IconButton>}
                                            <input
                                                type="text"
                                                readOnly={submitted}
                                                onChange={CreateQuestion}
                                                placeholder="Search or Select"
                                                aria-label="Search or select an option"
                                                style={{ flex: 1, padding: '8px', borderRadius: '4px', border: '1px solid #ccc' }}
                                            />
                                            <IconButton aria-label="Next" disabled={questionList?.length === 0} onClick={handleRightArrowClick}>
                                                <KeyboardDoubleArrowRightIcon />
                                            </IconButton>
                                        </div>
                                        {loadingQuestion &&
                                            <Typography style={{ justifyContent: 'center', alignItems: 'center', fontSize: '20' }}>
                                                Loading.....
                                            </Typography>
                                        }

                                        {visible && !loadingQuestion && (
                                            <ul className="dropdown" role="listbox" aria-expanded={query.length > 0}>
                                                {questionList && questionList.length > 0 ? (
                                                    questionList.map((item) => {
                                                        const sanitizedQuestion = item.question_text
                                                            .replace(/<p>/g, '<span style="display: flex;">')
                                                            .replace(/<\/p>/g, '</span>')
                                                            .replace(/&nbsp;/g, ' ');
                                                        const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');

                                                        const isSelected = Array.isArray(questionid) ? questionid.some(q => q === item.question_id) : questionid === item.question_id;

                                                        return (
                                                            <li
                                                                key={item.question_id}
                                                                role="option"
                                                                className="dropdown-item"
                                                                tabIndex={0}
                                                                aria-selected={selectedOptionnew?.question_id === item.question_id}
                                                                title={sanitizedPassage}
                                                                style={{
                                                                    backgroundColor: isSelected ? '#e0ffe0' : 'transparent',
                                                                    padding: '8px',
                                                                    borderRadius: '4px',
                                                                    marginBottom: '4px',
                                                                }}
                                                            >
                                                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                    <div id="searchQuestionsTag" dangerouslySetInnerHTML={{ __html: sanitizedQuestion }} />


                                                                    {!isSelected && (
                                                                        <Button
                                                                            id="questionadd"
                                                                            variant="outlined"
                                                                            color="primary"
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                handleAddQuestion(item);
                                                                            }}
                                                                            sx={{
                                                                                fontSize: '0.75rem',
                                                                                minWidth: '24px',
                                                                                minHeight: '24px',
                                                                                padding: '2px',
                                                                                borderRadius: '12px',
                                                                            }}
                                                                        >
                                                                            +
                                                                        </Button>
                                                                    )}
                                                                </div>
                                                            </li>
                                                        );
                                                    })
                                                ) : (
                                                    <li className="dropdown-item" role="option">
                                                        No results found
                                                    </li>
                                                )}

                                            </ul>
                                        )}
                                        {
                                            !loadingQuestion && selectedQuestions && selectedQuestions.length > 0 && (
                                                <>
                                                    <Typography>Selected Questions:</Typography>
                                                    {selectedQuestions.map((item, index) => {
                                                        const sanitizedQuestion = item.question_text
                                                            .replace(/<p>/g, '<span style="display: flex;">')
                                                            .replace(/<\/p>/g, '</span>')
                                                            .replace(/&nbsp;/g, ' ');


                                                        const sanitizedPassage = DOMPurify.sanitize(item.passage, sanitizeConfig).replace(/<[^>]*>/g, '').trim().replace(/&nbsp;/g, ' ');
                                                        return (
                                                            <Tooltip
                                                                key={index}
                                                                title={sanitizedPassage}
                                                                placement="top"
                                                            >
                                                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                    <Typography
                                                                        id="QuestionArea"
                                                                        dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                                                    />
                                                                    <Button onClick={() => handleDeselect(item.question_id)}>
                                                                        x
                                                                    </Button>
                                                                </div>
                                                            </Tooltip>
                                                        );
                                                    }
                                                    )}

                                                </>
                                            )
                                        }
                                    </div>
                                </Grid>


                            </Grid>

                            :
                            <Grid container spacing={2}>
                                <Grid item sm={12}>
                                    <FormControl fullWidth>

                                        <Typography style={{ marginBottom: '0px' }} color="primary" className={classes.background} gutterBottom variant="subtitle1">
                                            Select Level of Objective
                                        </Typography>
                                        <FormGroup className='FormCheck'>
                                            {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                                <FormControlLabel
                                                    key={skill}
                                                    control={
                                                        <Radio
                                                            id={skill}
                                                            checked={selectedSkills === skill}
                                                            onChange={handleChangeCheck}
                                                            value={skill}
                                                        />
                                                    }
                                                    label={skill}
                                                />
                                            ))}
                                        </FormGroup>
                                        {questionError && questionError.Objective && <FormHelperText error  >{questionError?.Objective}</FormHelperText >}

                                    </FormControl>
                                </Grid>

                                <Grid item xs={6} fullWidth>
                                    <FormControl className={classes.formControl} style={{ width: "100%" }}
                                    // error={touched.questionType && Boolean(errors.questionType)}
                                    >
                                        <InputLabel id="demo-simple-select-standard-label">Level*</InputLabel>
                                        <Select
                                            disabled={submitted}
                                            name="level"
                                            labelId="demo-simple-select-standard-label"
                                            id="level"
                                            label="Level"
                                            value={details.level}
                                            onChange={(e) => setDetails(prevDetails => ({
                                                ...prevDetails,
                                                level: e.target.value
                                            }))}
                                            displayEmpty
                                        >
                                            <MenuItem value="easy">Easy</MenuItem>
                                            <MenuItem value="medium">Medium</MenuItem>
                                            <MenuItem value="complex">Complex</MenuItem>
                                        </Select>
                                        {detailsError.level && (
                                            <FormHelperText error>{detailsError.level}</FormHelperText>
                                        )}
                                    </FormControl>
                                </Grid>


                                <>
                                    <Grid item xs={12}>
                                        <FormControl style={{ width: '100%' }} className={classes.formControl}
                                        //  error={touched.question && Boolean(errors.question)}
                                        >
                                            <Typography className={classes.background} color="primary" gutterBottom variant="subtitle1">
                                                Create Question*
                                            </Typography>
                                            <ReactQuill
                                                readOnly={submitted}
                                                theme="snow"
                                                id="questionText"
                                                name="question"
                                                defaultValue={mathsvalues.question}
                                                onChange={(value) => handleChangeMathsQuestion('question', value)}
                                                modules={modules}
                                                formats={formats}
                                                // onBlur={() => setTouched((prev) => ({ ...prev, question: true }))}
                                                fullWidth
                                            />
                                            {questionError && questionError.question && <FormHelperText error>{questionError?.question}</FormHelperText>}
                                        </FormControl>
                                    </Grid>


                                    {mathsvalues?.questionType === 'Maths' && (
                                        <>
                                            <Grid item xs={12}>
                                                {mathsvalues?.mcqOptions.map((opt, index) => (
                                                    <div key={opt.id} style={{ position: 'relative', marginTop: '10px' }}>
                                                        <Grid container spacing={2} alignItems="center">
                                                            <Grid item xs={12} style={{ display: 'flex', alignItems: 'end', marginLeft: 40 }}>
                                                                <ReactQuill
                                                                    readOnly={submitted}
                                                                    theme="snow"
                                                                    id={`optiontext-${index}`}

                                                                    name={`mcqQuestion-${index}`}
                                                                    value={opt.option}
                                                                    onChange={(value) => { handleMCQOptionChangeMaths(index, 'option', value); setQuestionError({ option: '', }) }}
                                                                    modules={modules}
                                                                    formats={formats}
                                                                    placeholder="Option"
                                                                    style={{ marginTop: 10, flex: 1 }}
                                                                />
                                                                <IconButton
                                                                    disabled={submitted}
                                                                    aria-label="delete"
                                                                    color="error"
                                                                    onClick={() => handleRemoveOptionMaths(index)}
                                                                    style={{ marginLeft: '-8px', marginTop: '-8px' }}
                                                                >
                                                                    <ClearIcon fontSize="small" />
                                                                </IconButton>
                                                                <FormControlLabel
                                                                    control={
                                                                        <Checkbox
                                                                            id='checkBoxMCQ'
                                                                            name={`mcqOptions.${index}.isCorrect`}
                                                                            checked={opt.isCorrect}
                                                                            onChange={() => handleMCQOptionChangeMaths(index, 'isCorrect', !opt.isCorrect)}
                                                                            disabled={!opt.option.trim() || submitted}
                                                                        />
                                                                    }
                                                                    label="Correct"
                                                                />
                                                            </Grid>


                                                        </Grid>
                                                    </div>
                                                ))}


                                                {submitError && (
                                                    <div style={{ color: 'red', marginBottom: '10px', marginLeft: '100px', fontSize: '12px' }}>
                                                        {submitError}
                                                    </div>
                                                )}

                                                {questionError && questionError.option && <FormHelperText error >{questionError?.option}</FormHelperText>}
                                                {questionError && questionError.correctAnswer && <FormHelperText error >{questionError?.correctAnswer}</FormHelperText>}

                                                <Button
                                                    disabled={submitted}
                                                    variant="contained"
                                                    color="primary"
                                                    id='AddoptionsVal'
                                                    onClick={handleAddOptionMaths}
                                                    style={{ width: '120px', backgroundColor: 'rgb(63, 186, 150)', marginTop: '10px', borderRadius: '6px' }}
                                                >
                                                    Add Option
                                                </Button>
                                                {requiredErrors.questionid && (
                                                    <FormHelperText error>{requiredErrors.questionid}</FormHelperText>
                                                )}
                                            </Grid>
                                            <Grid item xs={12} sm={12} sx={{ marginBottom: '15px', paddingRight: '18px' }}>
                                                <Typography variant="subtitle1">Explanation *</Typography>
                                                <ReactQuill
                                                    readOnly={submitted}
                                                    theme="snow"
                                                    id="explanation"
                                                    name="explanation"
                                                    modules={modules}
                                                    formats={formats}
                                                    defaultValue={explanation}
                                                    onChange={(content) => {
                                                        satExplanation(content);
                                                        setQuestionError({
                                                            explanation: ''
                                                        });
                                                    }}
                                                />
                                                {questionError && questionError.explanation && <FormHelperText error  >{questionError?.explanation}</FormHelperText >}
                                            </Grid>
                                        </>
                                    )}
                                </>

                                <Grid item xs={12}>
                                    <Button onClick={SubmitQuestion} type="submit"
                                        disabled={loadingnew || submitted}
                                        id={`btnIs${buttonText}`}
                                        variant="contained" color="primary" fullWidth>
                                        {buttonText}
                                    </Button>
                                </Grid>
                                {requiredErrors.questionid && (
                                    <FormHelperText error>{requiredErrors.questionid}</FormHelperText>
                                )}
                            </Grid>}
                    </DialogContent>


                    <DialogActions>
                        <Button id='SubmitionButtontxt' onClick={() => handleModuleSubmitCreateQuestion(selectIndex)} color="secondary">
                            Submit
                        </Button>
                        <Button id='CancelButtontxt' onClick={handleCloseMaths} color="primary">
                            Cancel
                        </Button>
                    </DialogActions>


                </Dialog>



                <Dialog open={editDialog} onClose={handleCloseEdit} fullWidth>
                    <DialogTitle>Edit Module</DialogTitle>
                    <DialogContent>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <TextField
                                    variant="outlined"
                                    fullWidth
                                    id="addname"
                                    label="Module Name"
                                    type="text"
                                    name="name"
                                    value={editData.name || ""}
                                    onChange={EditModule}
                                    sx={{
                                        // bgcolor: "#f0f0f0",
                                        borderRadius: 1,
                                        marginBottom: '24px',
                                        height: 36,
                                        '& .MuiInputBase-input': {
                                            fontSize: 14,
                                            padding: "8px 12px",
                                        }
                                    }}
                                />
                            </Grid>

                            <Grid item xs={12}>
                                <TextField
                                    variant="outlined"
                                    fullWidth
                                    id="addweight"
                                    label="Module Weight"
                                    type="text"
                                    name="weightage"
                                    value={editData.weightage || ""}
                                    onChange={EditModule}
                                    sx={{
                                        marginBottom: '20px',
                                        // bgcolor: "#f0f0f0",
                                        borderRadius: 1,
                                        height: 36,
                                        '& .MuiInputBase-input': {
                                            fontSize: 14,
                                            padding: "8px 12px",
                                        }
                                    }}
                                />
                            </Grid>
                        </Grid>
                    </DialogContent>

                    <DialogActions>
                        <Button id='btnUpdateVal' onClick={handleModuleUpdateQuestion} color="secondary">
                            Update
                        </Button>
                        <Button id='btnCancelVal' onClick={handleCloseEdit} color="primary">
                            Cancel
                        </Button>
                    </DialogActions>
                </Dialog>


                <Dialog open={Preview} onClose={handlePreview} fullWidth
                    sx={{
                        '& .MuiDialog-paper': {
                            maxHeight: '75vh !important',
                            overflow: 'hidden !important'
                        }
                    }}>
                    <DialogTitle>Preview</DialogTitle>
                    <DialogContent style={{ marginLeft: '18px' }}>
                        <ol type="1" style={{ paddingLeft: '2em', margin: 0 }}>
                            {previewData ? previewData?.questiondetails?.length > 0 && previewData?.questiondetails?.map((details, index) => {
                                const sanitizedQuestion = details?.question_text
                                    .replace(/<p>/g, '<span style="display: flex;">')
                                    .replace(/<\/p>/g, '</span>')
                                    .replace(/&nbsp;/g, ' ');
                                return (
                                    <li id="QuestionlistMakers">
                                        <Box style={{ display: 'flex', alignItems: 'center', marginBottom: '10px', justifyContent: 'space-between' }}>
                                            <Typography
                                                variant="para" style={{ lineHeight: '1.2', fontSize: '15px', fontWeight: '400', paddingRight: '15px' }}
                                                dangerouslySetInnerHTML={{ __html: sanitizedQuestion }}
                                            />

                                            {previewData && previewData?.type === 'Maths' && <IconButton id='EditIcon' onClick={() => handleEditIndividualQuestion(details?.question_text, previewData?.questions_list[index])} color="error">
                                                <EditIcon />
                                            </IconButton>}
                                            <IconButton onClick={() => DeleteQuestion(index)} color="error">
                                                < DeleteOutlinedIcon id={`DeleteIcon${index}`} style={{ color: "#ff4842" }} />
                                            </IconButton >
                                        </Box>
                                    </li>
                                )
                            })
                                :
                                <div style={{ textAlign: 'center', margin: '20px 0' }}>
                                    <CircularProgress />
                                </div>
                            }
                            {previewData && previewData?.questions_list?.length === 0 &&
                                <div style={{ textAlign: 'center', marginTop: '20px' }}>
                                    <Typography
                                        variant="para"
                                        style={{
                                            lineHeight: '1.2',
                                            fontSize: '15px',
                                            fontWeight: '400',
                                            paddingRight: '15px',
                                            display: 'inline-block'
                                        }}
                                    >
                                        No Questions Found
                                    </Typography>
                                </div>

                            }
                        </ol>
                    </DialogContent>

                    <DialogActions>
                        <Button id='btnCancelIs' onClick={handlePreview} color="primary">
                            Cancel
                        </Button>
                        <Button id='btnUpdateIs' onClick={() => handleUpdateQuestions(editIndexnew)} color="primary">
                            Update
                        </Button>
                    </DialogActions>
                </Dialog>


                <Dialog open={deleteOpen} onClose={handleDeleteClose} fullWidth>
                    <DialogContent className='GACognitivesection' sx={{ paddingTop: '25px !important' }}>
                        <Typography> Do you want to delete this module ?</Typography>
                    </DialogContent>
                    <DialogActions>
                        <Button id='modulepopSubmits' onClick={conformDelete} color="secondary">
                            ok
                        </Button>
                        <Button id='modulepopCancel' onClick={handleDeleteClose} color="primary">
                            Cancel
                        </Button>
                    </DialogActions>


                </Dialog>




                <DialogModal
                    open={mathsmodel}
                    handleClose={modelClose}
                >
                    <Typography style={{ marginBottom: 10 }}>Update Question</Typography>
                    <Grid container spacing={2}>
                        <Grid item xs={6}>
                            <FormControl className={classes.formControl}>
                                <InputLabel id="level-label">Level*</InputLabel>
                                <Select
                                    name="level"
                                    labelId="level-label"
                                    id="level"
                                    value={questionDetails.level || ''}
                                    onChange={(e) => setQuestionDetails((prev) => ({ ...prev, level: e.target.value }))}
                                    displayEmpty
                                >
                                    <MenuItem value="easy">Easy</MenuItem>
                                    <MenuItem value="medium">Medium</MenuItem>
                                    <MenuItem value="complex">Complex</MenuItem>
                                </Select>

                                {detailsError.level && (
                                    <FormHelperText error>{detailsError.level}</FormHelperText>
                                )}
                            </FormControl>
                        </Grid>

                        <Grid item>
                            <FormControl fullWidth variant="outlined">
                                <Typography className={classes.background} color="primary" gutterBottom variant="subtitle1">
                                    Select Level of Objective
                                </Typography>
                                <FormGroup className="FormCheck">
                                    {['Knowledge', 'Comprehension', 'Application', 'Analysis', 'Synthesis', 'Evaluation'].map((skill) => (
                                        <FormControlLabel
                                            key={skill}
                                            control={
                                                <Radio
                                                    checked={questionDetails.cognitive_skill_id === skill}
                                                    onChange={() => setQuestionDetails((prev) => ({ ...prev, cognitive_skill_id: skill }))}
                                                    value={skill}
                                                />
                                            }
                                            label={skill}
                                        />
                                    ))}
                                </FormGroup>
                            </FormControl>
                        </Grid>

                        <Grid item xs={12}>
                            <FormControl className={classes.formControl}>
                                <Typography className={classes.background} color="primary" gutterBottom variant="subtitle1">
                                    Update Question*
                                </Typography>
                                <ReactQuill
                                    theme="snow"
                                    id="question_text"
                                    name="question_text"
                                    value={questionDetails?.question_text || ''}
                                    onChange={(value) => handleQuillChange('question_text', value)}
                                    modules={modules}
                                    formats={formats}
                                    fullWidth
                                />
                            </FormControl>
                        </Grid>

                        {questionDetails && questionDetails.options?.length > 0 && questionDetails.options?.map((opt, index) => (
                            <Grid item xs={12} key={index}>
                                <div style={{ position: 'relative', marginTop: '10px' }}>
                                    <Grid container spacing={2} alignItems="center">
                                        <Grid item xs={12} style={{ display: 'flex', alignItems: 'end', marginLeft: 40 }}>
                                            <ReactQuill
                                                theme="snow"
                                                id={`optiontext${index}`}
                                                name="mcqQuestion"
                                                value={opt.option}
                                                onChange={(value) => handleMCQOptionPreviewEdit(index, 'option', value)}
                                                modules={modules}
                                                formats={formats}
                                                placeholder="Option"
                                                style={{ marginTop: 10, flex: 1 }}
                                            />


                                            <IconButton
                                                aria-label="delete"
                                                color="error"
                                                onClick={() => handleRemoveOptionPreviewEdit(index)}
                                                style={{ marginLeft: '-8px', marginTop: '-8px' }}
                                            >
                                                <ClearIcon fontSize="small" />
                                            </IconButton>
                                            <FormControlLabel
                                                control={
                                                    <Checkbox
                                                        checked={opt.isCorrect}
                                                        onChange={() => handleMCQOptionPreviewEdit(index, 'isCorrect', !opt.isCorrect)}
                                                        disabled={!opt.option.trim()}
                                                    />
                                                }
                                                label="Correct"
                                            />
                                        </Grid>
                                    </Grid>
                                </div>
                            </Grid>
                        ))}


                        <Grid item xs={12}>
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={handleAddOptionPreviewEdit}
                                style={{ width: '120px', backgroundColor: 'rgb(63, 186, 150)', marginTop: '10px', borderRadius: '6px' }}
                            >
                                Add Option
                            </Button>
                        </Grid>

                        <Grid item xs={12}>
                            <Typography variant="subtitle1">Explanation *</Typography>
                            <ReactQuill
                                theme="snow"
                                id="justification"
                                name="justification"
                                value={questionDetails?.justification || ''}
                                onChange={(value) => setQuestionDetails((prev) => ({ ...prev, justification: value }))}
                                modules={modules}
                                formats={formats}
                            />
                        </Grid>

                        <Grid item xs={12}>

                            {loadingQuestionnew === false ? <Button
                                id="subMitButton"
                                variant="contained"
                                color="primary"
                                onClick={UpdateIndividualQuestion}
                                fullWidth
                            >
                                Update Question
                            </Button>
                                :
                                <Button
                                    id="subMitButton"
                                    variant="contained"
                                    color="primary"

                                    fullWidth
                                >
                                    Loading...
                                </Button>}

                        </Grid>
                    </Grid>

                </DialogModal>

                <SnackBar open={openSnackbar} snackbarTitle={snackbarTitle} close={() => setOpenSnackbar(false)} />


            </Page >
        </>
    );

}

const useStyles = makeStyles(() => ({
    imgPreviewRoot: {
        borderRadius: '10px',
        padding: '0.8rem;',
    },
    fileImgSIze: {
        width: '100%',
        height: '120px',
        objectFit: 'cover',
        objectPosition: 'center',
        border: '1px solid #fff',
        borderRadius: '5px',
        boxShadow: '0 3px 10px rgb(0 0 0 / 20%)',
    },
    badgeAlign: {
        boxShadow: '0 2px 8px -5px #ff0000',
        color: '#FF0000',
        fontSize: '1.2rem',
        backgroundColor: '#fff',
        padding: '2px',
        borderRadius: '10px',
        cursor: 'pointer',
    },
    deleteLabel: {
        width: 'max-content',
        cursor: 'pointer',
    }
}));
export default SatTestUpdate;